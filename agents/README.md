# Fitness Coach AI Agent System

This system uses multiple specialized agents orchestrated by a main `FitnessOrchestrator` class to provide personalized fitness and nutrition guidance.

## Agent Architecture

The system consists of several specialized agents:

1. **ClassificationAgent**: Determines what domain a user message belongs to
2. **SocialEventAgent**: Handles conversations about social events (restaurants, parties, etc.)
3. **NutritionAgent**: Handles nutrition-related queries and meal planning
4. **GeneralAgent**: Handles general conversations that don't fit other categories

## API Routes and Workflow

### Main Workflow

The primary workflow follows these steps:

1. **User Input Processing** (`/api/chat`):
   - Adds the message to short-term memory (InMemoryVectorDB)
   - Stores it in long-term memory (LongTermVectorStore using Pinecone)
   - Classifies the domain using ClassificationAgent
   - Routes to the appropriate specialized agent
   - Stores the response in both short and long-term memory
   - Updates user profile and progress
   - Checks for needed follow-ups

2. **Classification** (`/api/classify`):
   - Determines the domain of the message (social_event, nutrition, general)

3. **Domain-Specific Agents**:
   - `/api/agents/nutrition`: Direct access to the nutrition agent
   - `/api/agents/social`: Direct access to the social event agent
   - `/api/agents/general`: Direct access to the general agent

4. **Memory Management**:
   - `/api/memory/short-term`: Get recent conversation history
   - `/api/memory/summarize`: Force summarization of older conversation

5. **Progress Tracking**:
   - `/api/progress`: Get user progress data
   - `/api/progress/weekly-summary`: Generate a weekly summary

6. **Automated Checkups**:
   - `/api/checkups/schedule`: Schedule a follow-up message
   - `/api/checkups`: Get scheduled checkups

7. **User State Management**:
   - `/api/user/state`: Get full user state
   - `/api/user/profile`: Get user profile
   - `/api/audit-log`: Get audit log of interactions

8. **Testing**:
   - `/api/test`: Run a quick test of the system

### Execution Order

When a user sends a message through the main `/api/chat` endpoint, the following happens in order:

1. **Memory Storage**:
   - User message added to short-term memory
   - User message stored in long-term vector database

2. **Classification**:
   - ClassificationAgent determines the domain

3. **Agent Routing**:
   - Based on classification, routes to the appropriate specialized agent:
     - Social events → SocialEventAgent
     - Nutrition → NutritionAgent
     - Other topics → GeneralAgent

4. **Response Generation**:
   - The specialized agent generates a response
   - Response is stored in both short and long-term memory

5. **Context Management**:
   - If conversation is too long, older parts are summarized
   - User profile is updated with any new information

6. **Progress Tracking**:
   - System tracks events like missed meals, unplanned foods, social events
   - Generates personalized tips based on patterns

7. **Automated Follow-ups**:
   - Checks if follow-up messages need to be scheduled
   - Processes any due checkups

## Running the API

To run the API:

```bash
cd agents
python -m src.main
```

The API will be available at http://127.0.0.1:8000

## API Documentation

Once the server is running, you can access the API documentation at:
http://127.0.0.1:8000/docs