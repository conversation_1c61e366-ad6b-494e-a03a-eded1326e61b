#!/bin/bash

# Start Redis using Docker Compose
echo "Starting Redis..."
docker compose -f docker-compose.redis.yml up -d

# Wait for <PERSON><PERSON> to be ready
echo "Waiting for <PERSON><PERSON> to be ready..."
until docker compose -f docker-compose.redis.yml exec redis redis-cli ping | grep -q PONG
do
  echo "Redis is not ready yet..."
  sleep 1
done

echo "Redis is ready!"
echo "<PERSON><PERSON> is running at localhost:6379"
echo "To stop <PERSON><PERSON>, run: docker compose -f docker-compose.redis.yml down"
