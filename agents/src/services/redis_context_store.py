import redis
import json
import copy

from typing import Dict, Any, Optional
from json.decoder import <PERSON><PERSON><PERSON>ecodeError

from src.config import settings, logger

class RedisContextStore:
    """
    Redis-based context store for user state management.
    Provides persistent storage with automatic expiration for user contexts.
    Supports separate conversation contexts for web app and WhatsApp users.
    """

    _instance = None
    # Default channel for backward compatibility
    DEFAULT_CHANNEL = "web"
    # Valid channels
    VALID_CHANNELS = ["web", "whatsapp"]

    @classmethod
    def get_instance(cls):
        """
        Get the singleton instance of the Redis context store.
        """
        if cls._instance is None:
            cls._instance = cls(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                ttl=settings.REDIS_TTL
            )
        return cls._instance

    def __init__(self, host: str = settings.REDIS_HOST, port: int = settings.REDIS_PORT, db: int = settings.REDIS_DB, 
                 password: Optional[str] = settings.REDIS_PASSWORD, ttl: int = settings.REDIS_TTL):
        """
        Initialize the Redis context store.

        Args:
            host: Redis host
            port: Redis port
            db: Redis database number
            password: Redis password (if required)
            ttl: Default TTL for keys in seconds (default: 14 hours)
        """
        self.redis = redis.Redis(
            host=host,
            port=port,
            db=db,
            password=password,
            decode_responses=True  # Automatically decode responses to strings
        )
        self.default_ttl = ttl

    def get_user_state(self, user_id: str, channel: str = None) -> Dict[str, Any]:
        """
        Get a user's state from Redis.
        If the user doesn't exist, creates a default state and saves it to Redis.

        Args:
            user_id: The user ID or WhatsApp number
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            The user state as a dictionary
        """
        # Validate and normalize channel
        channel = self._validate_channel(channel)

        # If this is a WhatsApp number and we're using the WhatsApp channel,
        # try to get the mapped user ID
        if channel == "whatsapp":
            # Check if this is a WhatsApp number that's mapped to a user ID
            mapped_user_id = self.get_user_id_from_whatsapp(user_id)
            if mapped_user_id:
                # Use the mapped user ID for shared profile data
                user_profile = self._get_user_profile(mapped_user_id)
            else:
                # No mapping exists, use empty profile
                user_profile = self._create_empty_profile()
        else:
            # For web channel, use the user's own profile
            user_profile = self._get_user_profile(user_id)

        # Get channel-specific context
        key = f"{channel}:user:context:{user_id}"
        data = self.redis.get(key)

        if data:
            try:
                state = json.loads(data)
                # Ensure the state has the latest profile data
                state["profile"] = user_profile

                # Ensure conversation_db_documents is initialized if not present
                if "conversation_db_documents" not in state:
                    state["conversation_db_documents"] = []

                    # Save the updated state back to Redis
                    self.set_user_state(user_id, state, channel=channel)

                return state
            except JSONDecodeError:
                # If data is corrupted, create default state
                default_state = self._create_default_state(user_profile)
                # Save the default state to Redis
                self.set_user_state(user_id, default_state, channel=channel)
                return default_state
        else:
            # User doesn't exist, create default state
            default_state = self._create_default_state(user_profile)
            # Save the default state to Redis
            self.set_user_state(user_id, default_state, channel=channel)
            return default_state

    def set_user_state(self, user_id: str, state: Dict[str, Any], ttl: Optional[int] = None, channel: str = None) -> bool:
        """
        Set a user's state in Redis with TTL.

        Args:
            user_id: The user ID or WhatsApp number
            state: The user state as a dictionary
            ttl: TTL in seconds (if None, uses default TTL)
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            True if successful, False otherwise
        """
        # Validate and normalize channel
        channel = self._validate_channel(channel)

        # If this is a WhatsApp number and we're using the WhatsApp channel,
        # also update the shared profile
        if channel == "whatsapp":
            # Check if this is a WhatsApp number that's mapped to a user ID
            mapped_user_id = self.get_user_id_from_whatsapp(user_id)
            if mapped_user_id:
                # Update the shared profile
                self._set_user_profile(mapped_user_id, state.get("profile", {}))
        else:
            # For web channel, update the user's own profile
            self._set_user_profile(user_id, state.get("profile", {}))

        # Set channel-specific context
        key = f"{channel}:user:context:{user_id}"
        ttl = ttl if ttl is not None else self.default_ttl

        # Handle conversation_db_documents serialization
        # This is needed because the conversation_db_documents field contains complex objects
        # that need special handling for serialization
        if "conversation_db_documents" in state and isinstance(state["conversation_db_documents"], list):
            # Make a deep copy to avoid modifying the original state

            state_copy = copy.deepcopy(state)

            # Ensure each document in conversation_db_documents is serializable
            for i, doc in enumerate(state_copy["conversation_db_documents"]):
                # Make sure metadata is serializable
                if "metadata" in doc and isinstance(doc["metadata"], dict):
                    # Convert any non-serializable values to strings
                    for k, v in doc["metadata"].items():
                        if not isinstance(v, (str, int, float, bool, list, dict, type(None))):
                            doc["metadata"][k] = str(v)

            # Use the modified copy for serialization
            state = state_copy

        try:
            serialized = json.dumps(state)
            return self.redis.setex(key, ttl, serialized)
        except (TypeError, JSONDecodeError) as e:
            # If serialization fails, log the error and return False
            logger.error(f"Error serializing state for user {user_id}: {str(e)}")

            # Try to serialize without conversation_db_documents as a fallback
            if "conversation_db_documents" in state:
                fallback_state = {k: v for k, v in state.items() if k != "conversation_db_documents"}
                try:
                    serialized = json.dumps(fallback_state)
                    return self.redis.setex(key, ttl, serialized)
                except (TypeError, JSONDecodeError) as e2:
                    logger.error(f"Fallback serialization also failed for user {user_id}: {str(e2)}")

            return False

    def delete_user_state(self, user_id: str, channel: str = None) -> bool:
        """
        Delete a user's state from Redis.

        Args:
            user_id: The user ID or WhatsApp number
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            True if successful, False otherwise
        """
        # Validate and normalize channel
        channel = self._validate_channel(channel)

        key = f"{channel}:user:context:{user_id}"
        return bool(self.redis.delete(key))

    def get_ttl(self, user_id: str, channel: str = None) -> int:
        """
        Get the TTL for a user's state.

        Args:
            user_id: The user ID or WhatsApp number
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            TTL in seconds, -1 if key exists but has no TTL, -2 if key doesn't exist
        """
        # Validate and normalize channel
        channel = self._validate_channel(channel)

        key = f"{channel}:user:context:{user_id}"
        return self.redis.ttl(key)

    def reset_ttl(self, user_id: str, ttl: Optional[int] = None, channel: str = None) -> bool:
        """
        Reset the TTL for a user's state.

        Args:
            user_id: The user ID or WhatsApp number
            ttl: New TTL in seconds (if None, uses default TTL)
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            True if successful, False otherwise
        """
        # Validate and normalize channel
        channel = self._validate_channel(channel)

        key = f"{channel}:user:context:{user_id}"
        ttl = ttl if ttl is not None else self.default_ttl
        return bool(self.redis.expire(key, ttl))

    def map_whatsapp_to_user(self, whatsapp_number: str, user_id: str) -> bool:
        """
        Map a WhatsApp number to a user ID.

        Args:
            whatsapp_number: The WhatsApp number
            user_id: The user ID to map to

        Returns:
            True if successful, False otherwise
        """
        key = f"user:mapping:{whatsapp_number}"
        return bool(self.redis.set(key, user_id))

    def get_user_id_from_whatsapp(self, whatsapp_number: str) -> Optional[str]:
        """
        Get the user ID mapped to a WhatsApp number.

        Args:
            whatsapp_number: The WhatsApp number

        Returns:
            The user ID if found, None otherwise
        """
        key = f"user:mapping:{whatsapp_number}"
        return self.redis.get(key)

    def _get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """
        Get a user's profile from Redis.
        If the profile doesn't exist, returns an empty profile.

        Args:
            user_id: The user ID

        Returns:
            The user profile as a dictionary
        """
        key = f"user:profile:{user_id}"
        data = self.redis.get(key)

        if data:
            try:
                return json.loads(data)
            except JSONDecodeError:
                # If data is corrupted, return empty profile
                return self._create_empty_profile()
        else:
            # Profile doesn't exist, return empty profile
            return self._create_empty_profile()

    def _set_user_profile(self, user_id: str, profile: Dict[str, Any]) -> bool:
        """
        Set a user's profile in Redis.

        Args:
            user_id: The user ID
            profile: The user profile as a dictionary

        Returns:
            True if successful, False otherwise
        """
        key = f"user:profile:{user_id}"

        try:
            serialized = json.dumps(profile)
            return bool(self.redis.set(key, serialized))
        except (TypeError, JSONDecodeError):
            # If serialization fails, return False
            return False

    def update_user_profile_from_api(self, user_id: str, api_data: Dict[str, Any], channel: str = None) -> bool:
        """
        Update a user's profile in Redis with data from the API.
        This is the preferred method to use instead of setting default values.

        Args:
            user_id: The user ID
            api_data: User data from the API
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate and normalize channel
            channel = self._validate_channel(channel)



            # Extract profile data from API data
            profile = self._transform_api_data_to_profile(api_data)

            # Set the profile in Redis
            success = self._set_user_profile(user_id, profile)

            # If this is a WhatsApp user, also update the state
            if channel == "whatsapp":
                # Get the current state
                key = f"{channel}:user:context:{user_id}"
                data = self.redis.get(key)

                if data:
                    try:
                        state = json.loads(data)
                        # Update the profile in the state
                        state["profile"] = profile
                        # Save the updated state back to Redis
                        self.set_user_state(user_id, state, channel=channel)
                    except JSONDecodeError:
                        # If data is corrupted, create a new state with the profile
                        state = self._create_default_state(profile)
                        self.set_user_state(user_id, state, channel=channel)
                else:
                    # If no state exists, create a new one with the profile
                    state = self._create_default_state(profile)
                    self.set_user_state(user_id, state, channel=channel)

            return success
        except Exception as e:
            print(f"Error updating user profile from API: {e}")
            logger.error(e)
            return False

    def _transform_api_data_to_profile(self, api_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform API data to a user profile format for Redis.

        Args:
            api_data: User data from the API

        Returns:
            User profile as a dictionary
        """
        # Initialize with empty profile
        profile = self._create_empty_profile()

        # Handle different possible data formats

        # Extract basic user data if available
        if "user" in api_data:
            user = api_data.get("user", {})
            if isinstance(user, dict):
                profile["name"] = user.get("name", "")

        # Extract trainee profile data if available
        if "traineeProfile" in api_data:
            trainee = api_data.get("traineeProfile", {})
            if isinstance(trainee, dict):
                profile["age"] = trainee.get("age", 0)
                profile["height"] = trainee.get("height", 0)
                profile["weight"] = trainee.get("weight", 0)
                profile["gender"] = trainee.get("gender", "")
                profile["activity_level"] = trainee.get("activityLevel", "")
                profile["goals"] = [trainee.get("fitnessGoal", "")] if trainee.get("fitnessGoal") else []

        # Extract meal plan data if available
        if "mealPlan" in api_data:
            meal_plan = api_data.get("mealPlan", [])
            if isinstance(meal_plan, list):
                profile["meals"] = meal_plan

        # Extract dietary restrictions if available
        if "dietaryRestrictions" in api_data:
            restrictions = api_data.get("dietaryRestrictions", [])
            if isinstance(restrictions, list):
                profile["dietary_restrictions"] = restrictions



        return profile

    def _validate_channel(self, channel: Optional[str]) -> str:
        """
        Validate and normalize the channel.

        Args:
            channel: The channel to validate

        Returns:
            The normalized channel
        """
        if not channel:
            return self.DEFAULT_CHANNEL

        channel = channel.lower()
        if channel not in self.VALID_CHANNELS:
            return self.DEFAULT_CHANNEL

        return channel

    def _create_empty_profile(self) -> Dict[str, Any]:
        """
        Create an empty user profile with minimal structure.
        This avoids setting default values and encourages fetching real data from the API.

        Returns:
            Empty user profile as a dictionary
        """
        return {
            "name": "",
            "meals": []
        }

    def _create_default_profile(self) -> Dict[str, Any]:
        """
        Create a default user profile.
        DEPRECATED: Use _create_empty_profile instead.
        This method is kept for backward compatibility.

        Returns:
            Default user profile as a dictionary
        """
        # Call the empty profile method instead
        return self._create_empty_profile()

    def _create_default_state(self, profile: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create a minimal user state with the provided profile.
        This avoids setting default values and encourages fetching real data from the API.

        Args:
            profile: The user profile to include in the state. If None, uses empty profile.

        Returns:
            Minimal user state as a dictionary
        """
        if profile is None:
            profile = self._create_empty_profile()

        return {
            "profile": profile,
            "progress": {
                "missed_meals_count": 0,
                "unplanned_food_count": 0,
                "social_events_count": 0,
                "personalized_tips": []
            },
            "audit_log": [],
            "summaries": [],
            "checkups": [],
            # Initialize an empty list for conversation_db documents
            # This will be converted to an InMemoryVectorDB instance by the orchestrator
            "conversation_db_documents": []
        }
