from typing import Dict, List, Any, Optional
from .redis_context_store import RedisContextStore


class UserService:
    """
    Service for managing user state using Redis for persistence.
    Supports separate conversation contexts for web app and WhatsApp users.
    Uses API data instead of default values.
    """
    # Default channel for backward compatibility
    DEFAULT_CHANNEL = "web"

    # Use Redis for user state storage with TTL
    @classmethod
    def _get_redis_store(cls):
        """
        Get the Redis context store instance
        """
        return RedisContextStore.get_instance()

    @classmethod
    def get_user_state(cls, user_id: str, channel: str = None) -> Dict[str, Any]:
        """
        Get or create a user state from Redis

        Args:
            user_id: The user ID or WhatsApp number
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            The user state as a dictionary
        """
        # Get the Redis store
        redis_store = cls._get_redis_store()

        # Get the user state from Redis with channel
        user_state = redis_store.get_user_state(user_id, channel)

        # Reset TTL on access to maintain active contexts
        redis_store.reset_ttl(user_id, channel=channel)

        return user_state

    @classmethod
    def get_user_profile(cls, user_id: str, channel: str = None) -> Dict[str, Any]:
        """
        Get a user's profile

        Args:
            user_id: The user ID or WhatsApp number
            channel: The channel (web or whatsapp). If None, uses default channel.
        """
        user_state = cls.get_user_state(user_id, channel)
        return user_state.get("profile", {})

    @classmethod
    def get_user_progress(cls, user_id: str, channel: str = None) -> Dict[str, Any]:
        """
        Get a user's progress data

        Args:
            user_id: The user ID or WhatsApp number
            channel: The channel (web or whatsapp). If None, uses default channel.
        """
        user_state = cls.get_user_state(user_id, channel)
        return user_state.get("progress", {})

    @classmethod
    def get_audit_log(cls, user_id: str, limit: int = 50, channel: str = None) -> List[Dict[str, Any]]:
        """
        Get a user's audit log

        Args:
            user_id: The user ID or WhatsApp number
            limit: Maximum number of log entries to return
            channel: The channel (web or whatsapp). If None, uses default channel.
        """
        user_state = cls.get_user_state(user_id, channel)
        audit_log = user_state.get("audit_log", [])
        return audit_log[-limit:] if limit > 0 else audit_log

    @classmethod
    def update_user_meals(cls, user_id: str, meals: List[Dict[str, Any]], channel: str = None) -> None:
        """
        Update a user's meal data in their profile

        Args:
            user_id: The user ID or WhatsApp number
            meals: List of meal data objects
            channel: The channel (web or whatsapp). If None, uses default channel.
        """
        # Get the user state
        user_state = cls.get_user_state(user_id, channel)

        # Update the meals
        user_state["profile"]["meals"] = meals

        # Save the updated state back to Redis
        redis_store = cls._get_redis_store()
        redis_store.set_user_state(user_id, user_state, channel=channel)



    @classmethod
    def save_user_state(cls, user_id: str, state: Dict[str, Any], channel: str = None) -> bool:
        """
        Save a user's state to Redis

        Args:
            user_id: The user ID or WhatsApp number
            state: The user state to save
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            True if successful, False otherwise
        """
        redis_store = cls._get_redis_store()
        return redis_store.set_user_state(user_id, state, channel=channel)

    @classmethod
    def delete_user_state(cls, user_id: str, channel: str = None) -> bool:
        """
        Delete a user's state from Redis

        Args:
            user_id: The user ID or WhatsApp number
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            True if successful, False otherwise
        """
        redis_store = cls._get_redis_store()
        return redis_store.delete_user_state(user_id, channel=channel)

    @classmethod
    def map_whatsapp_to_user(cls, whatsapp_number: str, user_id: str) -> bool:
        """
        Map a WhatsApp number to a user ID.

        Args:
            whatsapp_number: The WhatsApp number
            user_id: The user ID to map to

        Returns:
            True if successful, False otherwise
        """
        redis_store = cls._get_redis_store()
        return redis_store.map_whatsapp_to_user(whatsapp_number, user_id)

    @classmethod
    def get_user_id_from_whatsapp(cls, whatsapp_number: str) -> Optional[str]:
        """
        Get the user ID mapped to a WhatsApp number.

        Args:
            whatsapp_number: The WhatsApp number

        Returns:
            The user ID if found, None otherwise
        """
        redis_store = cls._get_redis_store()
        return redis_store.get_user_id_from_whatsapp(whatsapp_number)

    @classmethod
    def update_user_data_from_api(cls, user_id: str, api_data: Dict[str, Any], channel: str = None) -> bool:
        """
        Update a user's data in Redis with data from the API.
        This is the preferred method to use instead of setting default values.

        Args:
            user_id: The user ID
            api_data: User data from the API
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            True if successful, False otherwise
        """
        redis_store = cls._get_redis_store()
        return redis_store.update_user_profile_from_api(user_id, api_data, channel=channel)

    @classmethod
    def update_user_profile(cls, user_id: str, profile_data: Dict[str, Any], channel: str = None) -> bool:
        """
        Update a user's profile in Redis.

        Args:
            user_id: The user ID
            profile_data: User profile data
            channel: The channel (web or whatsapp). If None, uses default channel.

        Returns:
            True if successful, False otherwise
        """
        # Get the user state
        user_state = cls.get_user_state(user_id, channel)

        # Update the profile
        user_state["profile"].update(profile_data)

        # Save the updated state back to Redis
        redis_store = cls._get_redis_store()
        success = redis_store.set_user_state(user_id, user_state, channel=channel)

        return success
