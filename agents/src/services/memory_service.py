
from typing import Dict, List, Any

from .user_service import UserService
from .orchestrator_service import OrchestratorService

from src.config import logger

class MemoryService:
    """
    Service for managing conversation memory
    """
    @classmethod
    def get_short_term_memory(cls, user_id: str, limit: int = 20, channel: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get the short-term memory (recent conversation) for a user

        Args:
            user_id: The user ID
            limit: Maximum number of messages to return
            channel: The channel (web or whatsapp)
        """
        user_state = UserService.get_user_state(user_id, channel=channel)

        if "conversation_db" not in user_state:
            return {"messages": []}

        recent_docs = user_state["conversation_db"].get_recent_documents(n=limit)
        return {
            "messages": [
                {
                    "role": doc.get("metadata", {}).get("role", "unknown"),
                    "content": doc["content"],
                    "timestamp": doc.get("metadata", {}).get("timestamp", 0),
                    "domain": doc.get("metadata", {}).get("domain", "unknown")
                }
                for doc in recent_docs
            ]
        }

    @classmethod
    def summarize_conversation(cls, user_id: str, channel: str = None) -> Dict[str, str]:
        """
        Force a summarization of the conversation

        Args:
            user_id: The user ID
            channel: The channel (web or whatsapp)
        """

        user_state = UserService.get_user_state(user_id, channel=channel)

        # Get the orchestrator for this user
        orchestrator = OrchestratorService.get_orchestrator(user_id)

        # Summarize the conversation
        summary = orchestrator.summarize_old_conversation(user_state)
        return {"summary": summary or "No summary available"}

    @classmethod
    def get_conversation_summaries(cls, user_id: str, limit: int = 10, channel: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get conversation summaries for a user

        Args:
            user_id: The user ID
            limit: Maximum number of summaries to return
            channel: The channel (web or whatsapp)
        """
        try:
            # Get user state
            user_state = UserService.get_user_state(user_id, channel=channel)

            # Get the orchestrator for this user
            orchestrator = OrchestratorService.get_orchestrator(user_id)

            # Get summaries
            summaries = orchestrator.get_conversation_summaries(user_state, limit=limit)

            # Format the response
            formatted_summaries = [
                {
                    "id": summary.get("id", ""),
                    "content": summary.get("content", ""),
                    "timestamp": summary.get("metadata", {}).get("timestamp", 0),
                    "msg_count": summary.get("metadata", {}).get("msg_count", 0),
                    "start_time": summary.get("metadata", {}).get("start_msg_time", 0),
                    "end_time": summary.get("metadata", {}).get("end_msg_time", 0)
                }
                for summary in summaries
            ]

            return {
                "summaries": formatted_summaries
            }
        except Exception as e:
            logger.error(e)
            return {"summaries": []}
