import pinecone 
from typing import List, Dict, Any, Optional

from src.config import settings, client, logger
class LongTermVectorStore:
    """
    Uses Pinecone to store conversation chunks, user data, and summaries for
    long-term retrieval. Each entry is upserted with an embedding from OpenAI.

    User data is isolated by adding a user_id namespace to each document.
    """

    def __init__(self, index_name: str, user_id: Optional[str] = None):
        """
        Initialize the vector store with an index name and optional user_id.

        Args:
            index_name: The name of the Pinecone index
            user_id: The user ID to namespace the data (if None, no namespace is used)
        """
        pc = pinecone.Pinecone(api_key=settings.PINECONE_API_KEY)
        existing_indexes = pc.list_indexes().names()
        
        index_spec = {
            "dimension": 1536,
            "metric": "cosine",
            "cloud": "aws",
            "region": settings.PINECONE_ENVIRONMENT
        }

        if index_name not in existing_indexes:
            pc.create_index(
                name=index_name, spec=index_spec
            )
        self.index = pc.Index(index_name)
        self.user_id = user_id

    def embed_text(self, text: str) -> List[float]:
        """Use OpenAI's text-embedding-ada-002 to embed text."""
        emb_response = client.embeddings.create(
            model="text-embedding-ada-002",
            input=text
        )
        return emb_response.data[0].embedding

    def upsert_document(self, doc_id: str, content: str, metadata: Dict[str, Any]):
        """
        Embeds the content, then upserts to Pinecone.
        doc_id: unique identifier for the document
        content: the text
        metadata: dict with extra info (role, summary, user profile changes, etc.)

        If user_id is set, the document will be namespaced to that user.
        """
        vector = self.embed_text(content)

        # Add user_id to metadata for filtering
        if self.user_id:
            metadata["user_id"] = self.user_id
            # Prefix doc_id with user_id to ensure uniqueness across users
            doc_id = f"{self.user_id}_{doc_id}"

        # Store content in metadata for easier retrieval
        if "content" not in metadata:
            metadata["content"] = content

        self.index.upsert([(doc_id, vector, metadata)])

    def query(self, query_text: str, top_k: int = 10, filter_dict: Optional[Dict] = None, relevancy_threshold: float = 0.0) -> List[Dict[str, Any]]:
        """
        Semantic search for the top_k relevant docs in the index for query_text.
        Returns a list of matches with 'metadata'.

        Args:
            query_text: The text to search for
            top_k: Maximum number of results to return
            filter_dict: Optional dictionary of metadata filters to apply
            relevancy_threshold: Minimum similarity score (0.0 to 1.0) for results to be included

        If user_id is set, only documents for that user will be returned.
        """
        user_id = self.user_id or "default_user"

        q_vec = self.embed_text(query_text)

        # Add user_id to filter if set
        if self.user_id:
            if filter_dict is None:
                filter_dict = {}
            filter_dict["user_id"] = {"$eq": self.user_id}

        results = self.index.query(
            vector=q_vec,
            top_k=top_k,
            include_metadata=True,
            filter=filter_dict
        )

        docs = []
        all_scores = []

        for match in results.matches or []:
            all_scores.append(match.score)
            # Only include results that meet the relevancy threshold
            if match.score >= relevancy_threshold:
                docs.append({
                    "id": match.id,
                    "score": match.score,
                    "content": match.metadata.get("content", ""),
                    "metadata": match.metadata
                })


        return docs

    def query_by_metadata(self, filter_dict: Dict, sort_by: Optional[str] = None,
                         sort_order: str = "desc", limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Query documents by metadata filters with optional sorting and limiting.

        Args:
            filter_dict: Dictionary of metadata filters
            sort_by: Field to sort by
            sort_order: "asc" or "desc"
            limit: Maximum number of results to return

        Returns:
            List of matching documents
        """
        user_id = self.user_id or "default_user"

        # Add user_id to filter if set
        if self.user_id:
            filter_dict["user_id"] = {"$eq": self.user_id}

        try:
            # Query Pinecone with metadata filter
            # Use a dummy vector for metadata-only query
            results = self.index.query(
                vector=[0] * 1536,  # Dummy vector for metadata-only query
                top_k=1000,  # Large number to get all matches
                include_metadata=True,
                filter=filter_dict
            )

            # Process results
            docs = []
            for match in results.matches or []:
                docs.append({
                    "id": match.id,
                    "score": 1.0,  # Not a semantic match
                    "content": match.metadata.get("content", ""),
                    "metadata": match.metadata
                })

            # Sort if requested
            if sort_by and docs and sort_by in docs[0]["metadata"]:
                docs.sort(
                    key=lambda x: x["metadata"][sort_by],
                    reverse=(sort_order.lower() == "desc")
                )

            # Limit if requested
            if limit and isinstance(limit, int):
                docs = docs[:limit]

            return docs

        except Exception as e:
            logger.error(e)
            return []