from typing import Dict, Any, Optional
from src.middleware.contradiction_service import ContradictionService

class ContradictionMiddleware:
    """
    Middleware for detecting contradictions in user messages.

    This middleware checks for contradictions in meal-related statements
    like "I had breakfast" vs "I missed my breakfast" on the same day.
    """

    def __init__(self):
        """Initialize the contradiction middleware."""
        pass

    def process(self, user_input: str, global_state: Dict[str, Any]) -> Optional[str]:
        """
        Process the user input to check for contradictions.

        Args:
            user_input: The user message
            global_state: The global state dictionary

        Returns:
            A contradiction response if a contradiction is detected, None otherwise
        """
        # Skip contradiction check if no conversation database is available
        if "conversation_db" not in global_state:
            return None

        conversation_db = global_state["conversation_db"]

        # Check for contradictions
        has_contradiction, contradiction_type, meal_type = ContradictionService.check_for_contradiction(
            conversation_db, user_input
        )

        # If a contradiction is detected, generate a response
        if has_contradiction and contradiction_type and meal_type:
            response = ContradictionService.generate_contradiction_response(contradiction_type, meal_type)

            # Add to audit log if available
            if "audit_log" in global_state:
                global_state["audit_log"].append({
                    "type": "contradiction_detected",
                    "contradiction_type": contradiction_type,
                    "meal_type": meal_type,
                    "user_input": user_input
                })

            return response

        # No contradiction detected
        return None
