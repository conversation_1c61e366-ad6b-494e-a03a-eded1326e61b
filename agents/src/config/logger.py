"""
Centralized logger configuration for the Raeda AI agents.

This module provides a single logger instance that should be used throughout the agents codebase.
It configures logging to write to console only with appropriate formatting.
"""

import logging

# Configure logging to write to console only
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# Create a logger
logger = logging.getLogger("raeda-ai")

# Export the logger as a module-level variable
__all__ = ['logger']
