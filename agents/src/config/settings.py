from pydantic_settings import BaseSettings, SettingsConfigDict
import tiktoken
from typing import Optional

class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8")
    
    ENV: str = "dev"

    PORT: int = 8000

    # Agents settings
    OPENAI_API_KEY: str
    GPT_MODEL: str

    PINECONE_API_KEY: str
    PINECONE_ENVIRONMENT: str
    PINECONE_INDEX_NAME: str

    # Redis settings
    REDIS_HOST: str = 'redis'
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_TTL: int = 50400  # 14 hours in seconds

settings = Settings()

# Use cl100k_base encoding which is used by GPT-4 models
# This avoids the error when tiktoken doesn't recognize newer model names
ENCODING = tiktoken.get_encoding("cl100k_base")
