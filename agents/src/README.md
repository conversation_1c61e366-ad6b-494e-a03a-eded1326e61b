# Fitness Coach AI API - Code Structure

This document explains the code structure of the Fitness Coach AI API.

## Directory Structure

```
src/
├── agents/                 # Agent implementations
│   ├── __init__.py
│   ├── classification_agent.py
│   ├── general_agent.py
│   ├── nutrition_agent.py
│   ├── orchestrator.py     # Main orchestrator
│   └── social_agent.py
├── config/                 # Configuration
│   ├── __init__.py
│   ├── clients.py
│   ├── settings.py
│   └── system_prompts.py
├── models/                 # Pydantic models
│   ├── __init__.py
│   ├── request_models.py   # Request models
│   └── response_models.py  # Response models
├── routers/                # API routes
│   ├── __init__.py
│   ├── chat_router.py      # Chat endpoints
│   ├── checkup_router.py   # Checkup endpoints
│   ├── memory_router.py    # Memory endpoints
│   ├── progress_router.py  # Progress endpoints
│   ├── test_router.py      # Test endpoints
│   └── user_router.py      # User endpoints
├── services/               # Business logic
│   ├── __init__.py
│   ├── checkup_service.py  # Checkup management
│   ├── memory_service.py   # Memory management
│   └── user_service.py     # User state management
├── __init__.py
├── utils.py                # Utility functions
└── main.py                 # Main application entry point
```

## Code Organization

### 1. Models

The `models` directory contains Pydantic models for request and response validation:

- `request_models.py`: Models for API requests
- `response_models.py`: Models for API responses

### 2. Services

The `services` directory contains business logic:

- `user_service.py`: Manages user state
- `checkup_service.py`: Manages automated checkups
- `memory_service.py`: Manages conversation memory

### 3. Routers

The `routers` directory contains API routes:

- `chat_router.py`: Main chat endpoints
- `memory_router.py`: Memory management endpoints
- `progress_router.py`: Progress tracking endpoints
- `checkup_router.py`: Automated checkup endpoints
- `user_router.py`: User state management endpoints
- `test_router.py`: Test endpoints

### 4. Agents

The `agents` directory contains the agent implementations:

- `orchestrator.py`: Main orchestrator that coordinates the agents
- `classification_agent.py`: Classifies user messages
- `nutrition_agent.py`: Handles nutrition-related queries
- `social_agent.py`: Handles social event-related queries
- `general_agent.py`: Handles general queries

### 5. Main Application

The `main.py` file is the entry point for the application. It:

1. Initializes the FastAPI app
2. Creates the orchestrator
3. Includes the routers
4. Defines the root endpoint

## Flow of Execution

1. A request comes in to one of the API endpoints
2. The router handles the request and calls the appropriate service
3. The service performs business logic, often using the orchestrator
4. The orchestrator coordinates the agents to process the request
5. The response is returned to the client

This modular structure makes the code more maintainable and easier to extend.
