import time
import json

from typing import Dict, List, Any, Optional
from src.config import settings, logger, client
from src.utils import enhanced_summarize_text
from src.vectors import InMemoryVectorDB, LongTermVectorStore
from src.middleware.pass_through_middleware import ContradictionMiddleware

from .classification_agent import ClassificationAgent
from .social_agent import SocialEventAgent
from .nutrition_agent import NutritionAgent
from .general_agent import GeneralAgent

class FitnessOrchestrator:
    """
    The main orchestrator that:
     - Keeps track of short-term context (InMemoryVectorDB).
     - Stores each user & assistant message in Pinecone (LongTermVectorStore).
     - Classifies domain.
     - Routes message to the correct agent.
     - Summarizes older conversation chunks if necessary.
     - Updates user profile if new info is found.
     - Tracks progress (missed meals, unplanned foods) for weekly analytics.

    Each user has their own orchestrator instance to ensure data isolation.
    """

    def __init__(self, openai_api_key: str, user_id: Optional[str] = None):
        """
        Initialize a new orchestrator instance.

        Args:
            openai_api_key: The OpenAI API key to use
            user_id: The user ID to associate with this orchestrator.
                    If provided, all data will be namespaced to this user.
        """
        self.user_id = user_id

        # Create user-specific conversation database and long-term store
        self.conversation_db = InMemoryVectorDB(user_id=user_id)
        self.longterm_store = LongTermVectorStore(
            index_name=settings.PINECONE_INDEX_NAME,
            user_id=user_id
        )

        # Agents (these are stateless, so they can be shared)
        self.classifier_agent = ClassificationAgent(openai_api_key)
        self.social_event_agent = SocialEventAgent(openai_api_key)
        self.nutrition_agent = NutritionAgent(openai_api_key)
        self.general_agent = GeneralAgent(openai_api_key)

        # Middleware
        self.contradiction_middleware = ContradictionMiddleware()

    def process_user_input(self, user_input: str, global_state: Dict[str, Any]) -> str:
        """
        1) Add user input to short-term memory.
        2) Upsert user input to long-term store.
        3) [DISABLED] Check for contradictions using the contradiction middleware.
        4) Classify domain.
        5) Route to correct agent.
        6) Agent response added to short-term & long-term store.
        7) Possibly summarize older conversation if it grows large.
        8) Update user profile or progress analytics.
        9) Check if a weekly summary is requested or if it's time to show progress.

        Note: Contradiction detection is disabled. Messages go directly to the AI agent.
        """
        user_id = self.user_id or "default_user"

        # Guarantee audit log
        if "audit_log" not in global_state:
            global_state["audit_log"] = []

        # Load conversation_db_documents from global_state if available
        # This ensures that messages from previous sessions are loaded
        if "conversation_db_documents" in global_state and isinstance(global_state["conversation_db_documents"], list):

            # Make sure the documents are in the correct format
            for doc in global_state["conversation_db_documents"]:
                if "content" in doc:
                    # Add the document to the conversation_db
                    self.conversation_db.add_document(
                        content=doc["content"],
                        metadata=doc.get("metadata", {})
                    )

        # 1) Add to short-term memory
        self.conversation_db.add_document(
            content=user_input,
            metadata={"role": "user", "timestamp": time.time()}
        )

        # 2) Upsert into long-term store
        user_doc_id = f"user_{int(time.time())}"
        self.longterm_store.upsert_document(
            doc_id=user_doc_id,
            content=user_input,
            metadata={"role": "user", "content": user_input, "timestamp": time.time()}
        )

        global_state["audit_log"].append({
            "type": "user_message",
            "message": user_input
        })

        # Provide references and save conversation_db documents to global_state
        global_state["conversation_db"] = self.conversation_db
        global_state["conversation_db_documents"] = self.conversation_db.documents
        global_state["longterm_store"] = self.longterm_store

        # 3) Contradiction detection is disabled - messages go directly to the AI agent
        # The pass-through middleware always returns None, so this code will never execute
        # We keep the call to maintain the same code structure
        contradiction_response = self.contradiction_middleware.process(user_input, global_state)
        # This condition will never be true with the pass-through middleware
        if contradiction_response:
            pass  # This code will never execute

        # 4) Classify domain
        domain = self.classifier_agent.classify(user_input, global_state)

        # 5) Route to correct agent
        if domain == "social_event":
            response = self.social_event_agent.handle_message(user_input, global_state)
        elif domain == "nutrition":
            response = self.nutrition_agent.handle_message(user_input, global_state)
        else:
            response = self.general_agent.handle_message(user_input, global_state)

        # 6) Store agent response
        self.conversation_db.add_document(
            content=response,
            metadata={"role": "assistant", "domain": domain, "timestamp": time.time()}
        )
        assistant_doc_id = f"assistant_{int(time.time())}"
        self.longterm_store.upsert_document(
            doc_id=assistant_doc_id,
            content=response,
            metadata={"role": "assistant", "domain": domain, "content": response, "timestamp": time.time()}
        )

        # 7) Summarize older conversation if large
        # Check if we need to summarize (when we have more than 20 messages)
        if len(self.conversation_db.documents) >= 20:
            self.summarize_old_conversation(global_state)

        # 8) Track user progress, e.g., missed meals, unplanned foods
        self.update_user_profile_and_progress(domain, user_input, response, global_state)
        self.maybe_schedule_checkups(user_input, global_state)

        # 9) Optionally check if user or system requests a weekly summary
        #    (We could trigger this automatically or if user says "Show me my progress")
        if "show me my progress" in user_input.lower() or "weekly summary" in user_input.lower():
            progress_report = self.generate_weekly_summary(global_state)
            # Append the progress report to final response or handle as separate message
            response += "\n\n" + progress_report

        # Log final
        global_state["audit_log"].append({
            "type": "assistant_message",
            "domain": domain,
            "message": response
        })

        # Save the updated conversation_db documents back to global_state
        # This ensures that the documents are persisted between sessions
        global_state["conversation_db_documents"] = self.conversation_db.documents

        return response

    def summarize_old_conversation(self, global_state: Dict[str, Any]):
        """
        Summarize conversation chunks and store them as vectors.
        Each summary covers multiple conversation turns.
        """
        user_id = self.user_id or "default_user"

        # Only summarize if we have enough messages
        if len(self.conversation_db.documents) <= 20:
            return

        # Get the chunk to summarize (e.g., messages 0-15 if we have 20 messages)
        # Keep the 5 most recent turns directly in the context
        chunk_to_summarize = self.conversation_db.documents[:-5]

        # Only summarize if the chunk is substantial
        if len(chunk_to_summarize) < 5:
            return

        # Combine the messages into text
        combined_text = ""
        for doc in chunk_to_summarize:
            role = doc['metadata'].get('role', '?')
            content = doc['content']
            combined_text += f"{role.upper()}: {content}\n"

        # Create a summary using the enhanced summarization function
        summary = enhanced_summarize_text(combined_text, purpose="general")

        # Store the summary with metadata
        timestamp = int(time.time())
        summary_id = f"summary_{timestamp}"

        metadata = {
            "role": "summary",
            "timestamp": timestamp,
            "start_msg_time": chunk_to_summarize[0]['metadata'].get('timestamp', 0),
            "end_msg_time": chunk_to_summarize[-1]['metadata'].get('timestamp', 0),
            "msg_count": len(chunk_to_summarize),
            "content": summary  # Store content in metadata for easier retrieval
        }

        self.longterm_store.upsert_document(
            doc_id=summary_id,
            content=summary,
            metadata=metadata
        )

        # Remove summarized messages from short-term memory, keeping only the most recent 5 turns
        self.conversation_db.documents = self.conversation_db.documents[-5:]

        # Save the updated conversation_db documents back to global_state
        # This ensures that the documents are persisted between sessions after summarization
        global_state["conversation_db_documents"] = self.conversation_db.documents

        # Store summary in global state for reference
        if "summaries" not in global_state:
            global_state["summaries"] = []
        global_state["summaries"].append({
            "timestamp": timestamp,
            "summary_text": summary
        })

        return summary

    def get_conversation_summaries(self, global_state: Dict[str, Any], limit: int = 10):
        """
        Get the most recent conversation summaries for the user.

        Args:
            global_state: The global state dictionary
            limit: Maximum number of summaries to retrieve

        Returns:
            List of summaries with metadata
        """
        user_id = self.user_id or "default_user"

        # Get summaries from long-term store
        filter_dict = {"role": {"$eq": "summary"}}

        summaries = self.longterm_store.query_by_metadata(
            filter_dict=filter_dict,
            sort_by="timestamp",
            sort_order="desc",
            limit=limit
        )

        return summaries


    def parse_progress_event(
        self,
        conversation_db: InMemoryVectorDB,
        user_input: str,
        global_state: Dict[str, Any]
    ) -> List[Dict[str, str]]:
        """
        Uses an LLM to determine which progress-related events (missed meal, unplanned food, social event)
        are mentioned in the user_input in the context of the last N turns.

        Returns a list of dict events. Example:
          [
             {"type": "missed_meal", "which_meal": "Meal2", "is_new": "yes" },
             {"type": "unplanned_food", "food_desc": "pizza", "is_new": "no" },
             ...
          ]

        "is_new" can be "yes" or "no", indicating if it's a brand new occurrence or continuing a previously mentioned one.
        """
    # 1) Gather last 5-7 conversation messages
        last_n = conversation_db.get_recent_documents(n=5)
        last_text = ""
        for doc in last_n:
            role = doc["metadata"].get("role", "user")
            content = doc["content"]
            last_text += f"[{role}] {content}\n"

        # 2) System prompt to ask an LLM to parse:
        system_prompt = """
    You are a progress event parser in a fitness/nutrition system.

We have 3 main event types we track in progress:
1) "missed_meal" (user missed a meal)
2) "unplanned_food" (user ate something unplanned)
3) "social_event" (user is discussing or attending a social event: resterunt, bbq, vacation, any other social gathering)
The user may or may not mention them in their new message.

We have the last 5 conversation turns + the new user message.
Your job: read them, decide which events are mentioned (if any), and if each is "new" or "existing"
(i.e., continuing the same event from the conversation).

Output a JSON list of event objects, e.g.:
[
  { "type": "missed_meal", "which_meal": "Meal2", "is_new": "yes" },
  { "type": "unplanned_food", "food_desc": "pizza", "is_new": "no" },
  { "type": "social_event", "desc": "Restaurant dinner", "is_new": "yes" }
]
If no progress events are found, return an empty list [].

Constraints:
- Do NOT output any other keys or text.
- "is_new" must be "yes" or "no".
- For missed_meal, specify "which_meal" if possible (like "Meal4" or "Meal2"). If unknown, you can put "which_meal": "".
- For unplanned_food, put a short text in "food_desc" (like "pizza", "beer", etc.).
- For social_event, put "desc" with a short text describing it (like "barbecue", "restaurant", "vacation", etc.).
No additional explanation, only valid JSON.
"""

        # 3) Build the full messages for the LLM
        user_prompt = f"LAST CONVERSATION:\n{last_text}\n\nNEW USER MESSAGE:\n{user_input}\n\nReturn JSON list now."

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        # 4) Call LLM
        try:
            resp = client.chat.completions.create(
                model=settings.GPT_MODEL,
                messages=messages,
                temperature=0.2,
                max_tokens=2000
            )
            raw_json_str = resp.choices[0].message.content.strip()
        except Exception as e:
            # fallback: return empty
            logger.error(e)
            return []

        # 5) Attempt parse JSON

        try:
            event_list = json.loads(raw_json_str)
            if not isinstance(event_list, list):
                return []
            # Sanity check each item
            final_events = []
            for e in event_list:
                if isinstance(e, dict) and "type" in e and "is_new" in e:
                    final_events.append(e)
            return final_events
        except Exception as e:
            logger.error(e)
            return []

#new update user profile

    def update_user_profile_and_progress(
        self,
        domain: str,
        user_input: str,
        assistant_response: str,
        global_state: Dict[str, Any]
    ):
        # 0) Ensure progress data structure
        progress_data = global_state.setdefault("progress", {})
        progress_data.setdefault("missed_meals_count", 0)
        progress_data.setdefault("unplanned_food_count", 0)
        progress_data.setdefault("social_events_count", 0)
        progress_data.setdefault("personalized_tips", [])

        # 1) Parse events with LLM
        events = self.parse_progress_event(self.conversation_db, user_input, global_state)

        # 2) For each event in the list
        for ev in events:
            ev_type = ev.get("type")
            is_new = ev.get("is_new", "yes")

            if is_new == "yes":
                # a) Missed meal
                if ev_type == "missed_meal":
                    progress_data["missed_meals_count"] += 1
                    # Optionally store details if needed:
                    # e.g. progress_data.setdefault("missed_meals_detail", []).append(ev)

                # b) Unplanned food
                elif ev_type == "unplanned_food":
                    progress_data["unplanned_food_count"] += 1
                    # e.g. store details: progress_data.setdefault("unplanned_items", []).append(ev)

                # c) Social event
                elif ev_type == "social_event":
                    progress_data["social_events_count"] += 1
                    # e.g. store details: progress_data.setdefault("social_events", []).append(ev)

        # 3) Personalized tips logic
        if progress_data["missed_meals_count"] >= 3:
            tips = progress_data["personalized_tips"]
            if "Consider setting an alarm for Meal4" not in tips:
                tips.append("Consider setting an alarm for Meal4")

        # Save
        global_state["progress"] = progress_data

    def generate_weekly_summary(self, global_state: Dict[str, Any]) -> str:
        progress_data = global_state.get("progress", {})

        missed_meals = progress_data.get("missed_meals_count", 0)
        unplanned_foods = progress_data.get("unplanned_food_count", 0)
        social_events = progress_data.get("social_events_count", 0)
        personalized_tips = progress_data.get("personalized_tips", [])

        lines = []
        lines.append("דוח התקדמות שבועי שלך אח יקר:")
        lines.append(f"• פספסת {missed_meals} ארוחות השבוע")
        lines.append(f"• היו {unplanned_foods} אכילות לא מתוכננות השבוע")
        lines.append(f"• התקיימו {social_events} אירועים חברתיים השבוע")
        if personalized_tips:
            lines.append("• טיפים מותאמים אישית:")
            for tip in personalized_tips:
                lines.append(f"   - {tip}")

        lines.append("יאללה, תמשיך ככה גבר!")
        return "\n".join(lines)

###############################################################################
# 7) Automated Checkup & Reminders Integration
###############################################################################

    def parse_checkup_instructions(self, conversation_db, user_input, global_state) -> Dict[str, Any]:
        """
         Asks an LLM if a follow-up checkup or reminder is needed based on recent conversation.
         Returns a dict with fields like:
          {
             "checkup_needed": true/false,
             "checkup_text": "some text for next checkup",
             "time_in_minutes": 120
           }
          If no checkup is needed, it might return:
           {
             "checkup_needed": false
           }
         """

         # Gather last 5 turns for context
        recent_docs = conversation_db.get_recent_documents(n=7)
        last_convo_str = ""
        for doc in recent_docs:
            role = doc["metadata"].get("role","user")
            text = doc["content"]
            last_convo_str += f"[{role}] {text}\n"

         # Build system prompt for the LLM
        system_prompt = """
     You are a 'checkup scheduler' assistant in a fitness/nutrition system.

     Your role:
     1) Read the last 5 conversation turns in hebrew + the user's new message also in hebrew.
     2) Decide whether we should schedule an automated follow-up checkup or reminder for the user.
        For example, if you sense the user might need a motivational message or a nudge in X minutes/hours.
        instrucions for deciding if a check up is needed or not:

        - if the user asks for a checkup or a reminder, set it. for example If the user requests a reminder, e.g. “שלח לי תזכורת בעוד דקה” or “בעוד שעתיים תזכיר לי,”
        - if the user mentions going to a social event, any unsusual gathering set a check up to a certain time after the event
        - if the user missed a meal, or if the user ate something out of the mealplan, set a checkup to the next meal plan meal that checks that they remember the adaptation they need to make
        - make sure to not create double reminders.

     3)in anyone of the cases above, produce a json of this format: (define the "checkup_text", "time_in_minutes")
     {
       "checkup_needed": true,
       "checkup_text": "Your checkup message in Hebrew here",
       "time_in_minutes": 120
     }

     If NO checkup is needed, produce JSON:

     {
       "checkup_needed": false
     }

Constraints:
- Output must be valid JSON only, with no extra keys or text.
- time_in_minutes is an integer with how many minutes from now we should send the checkup.
- "checkup_text" must be Hebrew text the user will see.
- No additional explanation, code, or text. Only valid JSON.
"""

        user_prompt = f"LAST 5 TURNS:\n{last_convo_str}\n\nNEW USER MESSAGE:\n{user_input}\n\nReturn JSON now."


        # Build messages for LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        # Call the LLM
        try:
            resp = client.chat.completions.create(
                model=settings.GPT_MODEL,
                messages=messages,
                temperature=0.2,
                max_tokens=2000
            )
            raw_reply = resp.choices[0].message.content.strip()
        except Exception as e:
            # If something fails, default to no checkup
            logger.error(e)
            return {"checkup_needed": False}

        # Attempt to parse JSON
        try:
            data = json.loads(raw_reply)
            if not isinstance(data, dict):
                return {"checkup_needed": False}
            # Must at least have "checkup_needed" key
            if "checkup_needed" not in data:
                return {"checkup_needed": False}
            return data
        except Exception as e:
            logger.error(e)
            return {"checkup_needed": False}

    def maybe_schedule_checkups(self, user_input: str, global_state: Dict[str, Any]):
        """
        Called after we process user input. Asks the LLM if a checkup is needed.
        If yes, we add it to global_state["checkups"] = [] for later use.
        (Implementing the actual timed sending is up to you.)
         """
         # Ensure we have a place to store checkups
        if "checkups" not in global_state:
            global_state["checkups"] = []

        result = self.parse_checkup_instructions(self.conversation_db, user_input, global_state)
        if result.get("checkup_needed") is True:
             # e.g. store in global_state
           checkup_text = result.get("checkup_text", "")
           time_in_minutes = result.get("time_in_minutes", 60)

             # Add a new checkup entry
           new_checkup = {
              "text": checkup_text,
              "scheduled_in_minutes": time_in_minutes,
              "created_at": time.time()
           }
           global_state["checkups"].append(new_checkup)
