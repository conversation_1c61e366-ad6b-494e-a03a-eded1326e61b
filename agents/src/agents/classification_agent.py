from src.config import system_prompts
from typing import Dict, Any
from src.config.logger import logger
from src.agents.base_agent import BaseAgent

class ClassificationAgent(BaseAgent):
    VALID_DOMAINS = {"social_event", "nutrition", "general"}

    def __init__(self, openai_api_key: str, model_name: str = None):
        super().__init__(
            openai_api_key=openai_api_key,
            model_name=model_name,
            agent_name="ClassificationAgent",
            temperature=0.2,
            max_completion_tokens=30
        )

    def classify(self, user_input: str, global_state: Dict[str, Any]) -> str:
        """
        Uses DynamicPromptBuilder to construct a prompt with the three-tier context structure
        for classifying the user's message into one of the valid domains.
        """
        # Build the system prompt
        system_prompt = self._build_prompt(
            user_input=user_input,
            global_state=global_state,
            base_prompt=system_prompts.CLASSIFICATION_SYSTEM_PROMPT,
            domain="classification"
        )

        # Add the classification instruction
        system_prompt += f"\nUser's new message: {user_input}\n\nReturn only 'social_event', 'nutrition' or 'general'."

        # Prepare messages for OpenAI API
        messages = [
            {"role": "system", "content": system_prompt}
        ]

        # Call OpenAI API
        raw_response = self._call_openai_api(messages)

        # Process the response
        raw_domain = raw_response.strip().lower()
        if raw_domain not in self.VALID_DOMAINS:
            raw_domain = "general"

        # Log to audit (custom implementation for classification)
        if "audit_log" not in global_state:
            global_state["audit_log"] = []

        global_state["audit_log"].append({
            "type": "classification",
            "user_input": user_input,
            "classified_domain": raw_domain
        })
        logger.debug("[ClassificationAgent] Added classification entry to audit_log")

        return raw_domain