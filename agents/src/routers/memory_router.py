from fastapi import APIRouter, Depends
from typing import Optional

from src.services import UserService, MemoryService, OrchestratorService
from src.models import SummaryResponse, SummariesListResponse
from src.config import logger


router = APIRouter(prefix="/api/memory", tags=["memory"])

def get_orchestrator(user_id: str = None):
    """
    Dependency to get a user-specific orchestrator instance.

    Args:
        user_id: The user ID to get an orchestrator for.
                If None, returns the default orchestrator.

    Returns:
        A FitnessOrchestrator instance specific to the user
    """
    return OrchestratorService.get_orchestrator(user_id)

@router.get("/short-term")
async def get_short_term_memory(user_id: str = "default_user", channel: str = "web"):
    """
    Get the short-term memory (recent conversation) for a user

    Args:
        user_id: The user ID
        channel: The channel (web or whatsapp)
    """
    return MemoryService.get_short_term_memory(user_id, channel=channel)

@router.post("/summarize", response_model=SummaryResponse)
async def summarize_conversation(
    user_id: str = "default_user",
    channel: str = "web",
    orchestrator_instance = Depends(get_orchestrator)
):
    """
    Force a summarization of the conversation

    Args:
        user_id: The user ID
        channel: The channel (web or whatsapp)
    """
    # Get user-specific orchestrator
    user_orchestrator = OrchestratorService.get_orchestrator(user_id)

    user_state = UserService.get_user_state(user_id, channel=channel)
    summary = user_orchestrator.summarize_old_conversation(user_state)
    return {"summary": summary or "No summary available"}

@router.get("/summaries", response_model=SummariesListResponse)
async def get_summaries(
    user_id: str = "default_user",
    limit: Optional[int] = 10,
    channel: Optional[str] = "web"
):
    """
    Get conversation summaries for a user

    Args:
        user_id: The user ID
        limit: Maximum number of summaries to return
        channel: The channel (web or whatsapp)
    """
    try:
        result = MemoryService.get_conversation_summaries(user_id, limit=limit, channel=channel)
        return result
    except Exception as e:
        logger.error(e)
        return {"summaries": []}
