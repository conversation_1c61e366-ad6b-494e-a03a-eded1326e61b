from fastapi import APIRouter, Depends

from src.services import UserService, OrchestratorService
from src.models import WeeklySummaryRequest, SummaryResponse, ProgressResponse

router = APIRouter(prefix="/api/progress", tags=["progress"])

def get_orchestrator(user_id: str = None):
    """
    Dependency to get a user-specific orchestrator instance.

    Args:
        user_id: The user ID to get an orchestrator for.
                If None, returns the default orchestrator.

    Returns:
        A FitnessOrchestrator instance specific to the user
    """
    return OrchestratorService.get_orchestrator(user_id)

@router.get("", response_model=ProgressResponse)
async def get_progress(user_id: str = "default_user"):
    """
    Get the user's progress data
    """
    return UserService.get_user_progress(user_id)

@router.post("/weekly-summary", response_model=SummaryResponse)
async def generate_weekly_summary(
    request: WeeklySummaryRequest,
    orchestrator_instance = Depends(get_orchestrator)
):
    """
    Generate a weekly summary of the user's progress
    """
    # Get user-specific orchestrator
    user_orchestrator = OrchestratorService.get_orchestrator(request.user_id)

    user_state = UserService.get_user_state(request.user_id)
    summary = user_orchestrator.generate_weekly_summary(user_state)
    return {"summary": summary}
