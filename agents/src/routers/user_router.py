from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List, Optional
from pydantic import BaseModel

from src.services import UserService
from src.models import AuditLogResponse, WhatsAppMappingRequest

# NOT BEING USED, MAYBE DELETE?
class MealData(BaseModel):
    """
    Model for meal data
    """
    name: str
    foods: List[Dict[str, Any]]
    time: Optional[str] = None

# NOT BEING USED, MAYBE DELETE?
class TraineeProfileData(BaseModel):
    """
    Model for trainee profile data
    """
    age: Optional[int] = None
    gender: Optional[str] = None
    height: Optional[float] = None
    weight: Optional[float] = None
    bodyFatPercentage: Optional[float] = None
    fitnessGoal: Optional[str] = None
    activityLevel: Optional[str] = None

# NOT BEING USED, MAYBE DELETE?
class UserData(BaseModel):
    """
    Model for user data from the API
    """
    user: Optional[Dict[str, Any]] = None
    traineeProfile: Optional[TraineeProfileData] = None
    mealPlan: Optional[List[Dict[str, Any]]] = None
    dietaryRestrictions: Optional[List[str]] = None
    trainingPlan: Optional[Dict[str, Any]] = None

router = APIRouter(tags=["user"])

# User endpoints
@router.get("/api/user/state")
async def get_user_state_endpoint(user_id: str = "default_user", channel: str = None):
    """
    Get the full user state

    Args:
        user_id: The user ID
        channel: The channel (web or whatsapp)
    """
    return UserService.get_user_state(user_id, channel=channel)

@router.get("/api/user/profile")
async def get_user_profile(user_id: str = "default_user", channel: str = None):
    """
    Get the user profile

    Args:
        user_id: The user ID
        channel: The channel (web or whatsapp)
    """
    return UserService.get_user_profile(user_id, channel=channel)

# WhatsApp mapping endpoints
@router.post("/api/user/whatsapp-mapping")
async def map_whatsapp_to_user(mapping: WhatsAppMappingRequest):
    """
    Map a WhatsApp number to a user ID
    """
    if not mapping.whatsapp_number or not mapping.user_id:
        raise HTTPException(status_code=400, detail="WhatsApp number and user ID are required")

    success = UserService.map_whatsapp_to_user(mapping.whatsapp_number, mapping.user_id)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to map WhatsApp number to user ID")

    return {"success": True, "message": f"WhatsApp number {mapping.whatsapp_number} mapped to user ID {mapping.user_id}"}

@router.get("/api/user/whatsapp-mapping/{whatsapp_number}")
async def get_user_id_from_whatsapp(whatsapp_number: str):
    """
    Get the user ID mapped to a WhatsApp number
    """
    if not whatsapp_number:
        raise HTTPException(status_code=400, detail="WhatsApp number is required")

    user_id = UserService.get_user_id_from_whatsapp(whatsapp_number)
    if not user_id:
        raise HTTPException(status_code=404, detail=f"No user ID found for WhatsApp number {whatsapp_number}")

    return {"whatsapp_number": whatsapp_number, "user_id": user_id}

# Update user data from API
@router.post("/api/user/update-from-api")
async def update_user_data_from_api(user_data: Dict[str, Any], user_id: str, channel: str = None):
    """
    Update a user's data in Redis with data from the API.
    This is the preferred method to use instead of setting default values.

    Args:
        user_data: User data from the API
        user_id: The user ID (from query parameter)
        channel: The channel (web or whatsapp) (from query parameter)
    """
    success = UserService.update_user_data_from_api(user_id, user_data, channel=channel)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to update user data from API")

    return {"success": True, "message": f"User data updated for user ID {user_id}"}

# Update user profile directly
@router.post("/api/user/profile")
async def update_user_profile(profile_data: Dict[str, Any], user_id: str, channel: str = None):
    """
    Update a user's profile in Redis.

    Args:
        profile_data: User profile data
        user_id: The user ID (from query parameter)
        channel: The channel (web or whatsapp) (from query parameter)
    """
    success = UserService.update_user_profile(user_id, profile_data, channel=channel)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to update user profile")

    return {"success": True, "message": f"User profile updated for user ID {user_id}"}

# Audit log endpoint is outside the user router for better URL structure
@router.get("/api/audit-log", response_model=AuditLogResponse)
async def get_audit_log(user_id: str = "default_user", limit: int = 50, channel: str = None):
    """
    Get the audit log for a user

    Args:
        user_id: The user ID
        limit: Maximum number of log entries to return
        channel: The channel (web or whatsapp)
    """
    audit_log = UserService.get_audit_log(user_id, limit, channel=channel)
    return {"audit_log": audit_log}
