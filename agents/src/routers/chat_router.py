from fastapi import APIRouter, BackgroundTasks, Depends
from typing import Dict, Any, List
import re

from src.models import UserInput, AgentResponse, ClassificationRequest, ClassificationResponse
from src.services import UserService, CheckupService, OrchestratorService


router = APIRouter(prefix="/api", tags=["chat"])

def extract_meal_data_from_context(input_data: str) -> List[Dict[str, Any]]:
    """
    Extract meal data from the context string in the input data.

    Args:
        input_data: The input data string that may contain meal information

    Returns:
        A list of meal data dictionaries or an empty list if no meal data found
    """
    # Check if the input contains meal plan details
    meal_plan_match = re.search(r'\n\nMeal Plan Details:(.*?)\]', input_data, re.DOTALL)
    if not meal_plan_match:
        return []

    meal_data = meal_plan_match.group(1).strip()

    # Parse the meal data
    meals = []

    # Split by meal entries
    meal_entries = re.findall(r'Meal \d+: (.*?)(?=\nMeal \d+:|$)', meal_data, re.DOTALL)

    for entry in meal_entries:
        lines = entry.strip().split('\n')

        # Get meal name and description
        meal_header = lines[0]
        name_desc_parts = meal_header.split(' - ', 1)

        meal = {
            "name": name_desc_parts[0].strip(),
            "description": name_desc_parts[1].strip() if len(name_desc_parts) > 1 else "",
            "categories": []
        }

        # Process categories and food items
        current_category = None

        for line in lines[1:]:
            line = line.strip()

            # Category line
            if line.startswith('Categories:'):
                continue

            # Category name
            elif line.startswith('  ') and not line.startswith('    '):
                category_name = line.strip().rstrip(':')
                current_category = {"name": category_name, "options": []}
                meal["categories"].append(current_category)

            # Food item
            elif line.startswith('      ') and current_category:
                food_parts = line.split(' - Amount: ')
                if len(food_parts) == 2:
                    food_name = food_parts[0].strip()
                    amount = food_parts[1].strip()
                    current_category["options"].append({
                        "food": {"name": food_name},
                        "amount": amount
                    })

            # Macros
            elif line.startswith('  Macros:'):
                macro_text = line.replace('  Macros: ', '')
                # Parse macros (simplified)
                meal["macroRange"] = {"min": {}, "max": {}}

                # Extract protein
                protein_match = re.search(r'Protein: (\d+\.?\d*)g-(\d+\.?\d*)g', macro_text)
                if protein_match:
                    meal["macroRange"]["min"]["protein"] = float(protein_match.group(1))
                    meal["macroRange"]["max"]["protein"] = float(protein_match.group(2))

                # Extract carbs
                carbs_match = re.search(r'Carbs: (\d+\.?\d*)g-(\d+\.?\d*)g', macro_text)
                if carbs_match:
                    meal["macroRange"]["min"]["carbs"] = float(carbs_match.group(1))
                    meal["macroRange"]["max"]["carbs"] = float(carbs_match.group(2))

                # Extract fats
                fats_match = re.search(r'Fats: (\d+\.?\d*)g-(\d+\.?\d*)g', macro_text)
                if fats_match:
                    meal["macroRange"]["min"]["fats"] = float(fats_match.group(1))
                    meal["macroRange"]["max"]["fats"] = float(fats_match.group(2))

                # Extract calories
                calories_match = re.search(r'Calories: (\d+\.?\d*)-(\d+\.?\d*)', macro_text)
                if calories_match:
                    meal["macroRange"]["min"]["calories"] = float(calories_match.group(1))
                    meal["macroRange"]["max"]["calories"] = float(calories_match.group(2))

        meals.append(meal)

    return meals

def get_orchestrator(user_id: str = None):
    """
    Dependency to get a user-specific orchestrator instance.

    Args:
        user_id: The user ID to get an orchestrator for.
                If None, returns the default orchestrator.

    Returns:
        A FitnessOrchestrator instance specific to the user
    """
    return OrchestratorService.get_orchestrator(user_id)

@router.post("/chat", response_model=AgentResponse)
async def process_user_input(
    payload: UserInput,
    background_tasks: BackgroundTasks,
    orchestrator_instance = Depends(get_orchestrator)
):
    """
    Main endpoint that processes user input through the complete agent workflow:
    1. Adds message to memory
    2. Classifies the domain
    3. Routes to appropriate agent
    4. Stores response in memory
    5. Updates user profile and progress
    6. Checks for needed follow-ups
    """
    # Extract meal data from context if available
    meals = extract_meal_data_from_context(payload.input_data)
    if meals:
        # Update user profile with meal data
        UserService.update_user_meals(payload.user_id, meals, channel=payload.channel)

    # Get user-specific orchestrator
    user_orchestrator = OrchestratorService.get_orchestrator(payload.user_id)

    # Get user state with channel
    user_state = UserService.get_user_state(payload.user_id, channel=payload.channel)

    # Process the input through the user's orchestrator
    bot_reply = user_orchestrator.process_user_input(payload.input_data, user_state)

    # Ensure conversation_db_documents is updated in user_state
    if hasattr(user_orchestrator, "conversation_db") and hasattr(user_orchestrator.conversation_db, "documents"):

        # Create a serializable version of the documents
        serializable_documents = []
        for doc in user_orchestrator.conversation_db.documents:
            # Create a copy of the document with only serializable fields
            serializable_doc = {
                "content": doc.get("content", ""),
                "metadata": {}
            }

            # Copy serializable metadata
            if "metadata" in doc and isinstance(doc["metadata"], dict):
                for k, v in doc["metadata"].items():
                    if isinstance(v, (str, int, float, bool, list, dict, type(None))):
                        serializable_doc["metadata"][k] = v
                    else:
                        # Convert non-serializable values to strings
                        serializable_doc["metadata"][k] = str(v)

            serializable_documents.append(serializable_doc)

        # Ensure conversation_db_documents is updated in user_state with serializable documents
        user_state["conversation_db_documents"] = serializable_documents

    # Get the domain from the last audit log entry
    domain = "general"
    if user_state["audit_log"]:
        last_entry = user_state["audit_log"][-1]
        if "domain" in last_entry:
            domain = last_entry["domain"]

    # Remove non-serializable objects from user_state before saving
    # Create a clean copy of the user_state without any non-serializable objects
    clean_state = {}
    for key, value in user_state.items():
        if key not in ["longterm_store", "conversation_db"]:
            clean_state[key] = value

    # Save the clean state
    UserService.save_user_state(payload.user_id, clean_state, channel=payload.channel)

    # Check for scheduled checkups in the background
    background_tasks.add_task(CheckupService.process_checkups, payload.user_id)

    return {"response": bot_reply, "domain": domain}

@router.post("/classify", response_model=ClassificationResponse)
async def classify_input(
    payload: ClassificationRequest,
    orchestrator_instance = Depends(get_orchestrator)
):
    """
    Classifies the user input into a domain (social_event, nutrition, or general)
    without generating a response
    """
    # Get user-specific orchestrator
    user_orchestrator = OrchestratorService.get_orchestrator(payload.user_id)

    user_state = UserService.get_user_state(payload.user_id, channel=payload.channel if hasattr(payload, 'channel') else None)
    domain = user_orchestrator.classifier_agent.classify(payload.input_data, user_state)
    return {"domain": domain}

@router.post("/agents/nutrition")
async def nutrition_agent(
    payload: UserInput,
    orchestrator_instance = Depends(get_orchestrator)
):
    """
    Directly access the nutrition agent without going through classification
    """
    # Extract meal data from context if available
    meals = extract_meal_data_from_context(payload.input_data)
    if meals:
        # Update user profile with meal data
        UserService.update_user_meals(payload.user_id, meals, channel=payload.channel)

    # Get user-specific orchestrator
    user_orchestrator = OrchestratorService.get_orchestrator(payload.user_id)

    user_state = UserService.get_user_state(payload.user_id, channel=payload.channel)
    response = user_orchestrator.nutrition_agent.handle_message(payload.input_data, user_state)
    return {"response": response, "domain": "nutrition"}

@router.post("/agents/social")
async def social_agent(
    payload: UserInput,
    orchestrator_instance = Depends(get_orchestrator)
):
    """
    Directly access the social event agent without going through classification
    """
    # Get user-specific orchestrator
    user_orchestrator = OrchestratorService.get_orchestrator(payload.user_id)

    user_state = UserService.get_user_state(payload.user_id, channel=payload.channel)
    response = user_orchestrator.social_event_agent.handle_message(payload.input_data, user_state)
    return {"response": response, "domain": "social_event"}

@router.post("/agents/general")
async def general_agent(
    payload: UserInput,
    orchestrator_instance = Depends(get_orchestrator)
):
    """
    Directly access the general agent without going through classification
    """
    # Get user-specific orchestrator
    user_orchestrator = OrchestratorService.get_orchestrator(payload.user_id)

    user_state = UserService.get_user_state(payload.user_id, channel=payload.channel)
    response = user_orchestrator.general_agent.handle_message(payload.input_data, user_state)
    return {"response": response, "domain": "general"}
