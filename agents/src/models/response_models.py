from pydantic import BaseModel
from typing import List, Dict, Any, Optional

class ClassificationResponse(BaseModel):
    """
    Response model for classification requests
    """
    domain: str

class AgentResponse(BaseModel):
    """
    Response model for agent responses
    """
    response: str
    domain: str

class MessageResponse(BaseModel):
    """
    Model for a single message in the conversation history
    """
    role: str
    content: str
    timestamp: float
    domain: str

class ConversationHistoryResponse(BaseModel):
    """
    Response model for conversation history
    """
    messages: List[Dict[str, Any]]

class SummaryResponse(BaseModel):
    """
    Response model for conversation summaries
    """
    summary: str

class SummaryItem(BaseModel):
    """
    Model for a single summary item
    """
    id: str
    content: str
    timestamp: float
    msg_count: int
    start_time: float
    end_time: float

class SummariesListResponse(BaseModel):
    """
    Response model for a list of conversation summaries
    """
    summaries: List[SummaryItem]

class ProgressResponse(BaseModel):
    """
    Response model for user progress data
    """
    missed_meals_count: int = 0
    unplanned_food_count: int = 0
    social_events_count: int = 0
    personalized_tips: List[str] = []

class CheckupResponse(BaseModel):
    """
    Response model for scheduling checkups
    """
    checkup_id: str
    scheduled_time: float

class CheckupsListResponse(BaseModel):
    """
    Response model for listing checkups
    """
    checkups: List[Dict[str, Any]]

class AuditLogResponse(BaseModel):
    """
    Response model for audit logs
    """
    audit_log: List[Dict[str, Any]]

class TestResponse(BaseModel):
    """
    Response model for the test endpoint
    """
    bot_reply: str
    short_term_docs: List[Dict[str, Any]]
    audit_log: List[Dict[str, Any]]

class ApiInfoResponse(BaseModel):
    """
    Response model for the root endpoint
    """
    message: str
    version: str
    endpoints: Dict[str, Any]

class UserProfileData(BaseModel):
    """
    Model for user profile data from the API
    """
    name: Optional[str] = None
    age: Optional[int] = None
    height: Optional[float] = None
    weight: Optional[float] = None
    gender: Optional[str] = None
    activity_level: Optional[str] = None
    goals: Optional[List[str]] = None
    dietary_restrictions: Optional[List[str]] = None