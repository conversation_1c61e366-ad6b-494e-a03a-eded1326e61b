# Stage 1: Build the app
FROM node:20-alpine AS builder

WORKDIR /app

# Install dependencies
COPY ./package*.json ./
RUN npm install

# Copy source files
COPY ./tsconfig*.json ./
COPY ./src ./src

# Build the app
RUN npm run build

# Stage 2: Run the app
FROM node:20-alpine

WORKDIR /app

# Install only production dependencies
COPY ./package*.json ./
RUN npm install --only=production

# Copy the built output from the builder stage
COPY --from=builder /app/dist ./dist

# Copy any extra files needed at runtime (e.g., .env if baked in image)
# COPY api/.env .env

ENV NODE_ENV=production

CMD ["node", "dist/main.js"]
