import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import * as admin from 'firebase-admin';
import * as fs from 'fs';

@Injectable()
export class FirebaseService {
  private firebaseApp: admin.app.App;
  private readonly logger = new Logger(FirebaseService.name);

  constructor() {
    try {
      if (!admin.apps.length) {
        const serviceAccount = JSON.parse(
          fs.readFileSync(process.env.PERSONAL_FIREBASE_CREDENTIALS, 'utf8'),
        );

        this.firebaseApp = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
        });
      } else {
        this.firebaseApp = admin.app();
      }
    } catch (error) {
      this.logger.error('Firebase initialization failed', error.stack);
      throw error;
    }
  }

  async getUserUidByEmail(email: string): Promise<string | null> {
    try {
      const userRecord = await admin.auth().getUserByEmail(email);
      return userRecord.uid;
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        return null;
      }
      this.logger.error(`Error fetching user by email: ${email}`, error.stack);
      throw error;
    }
  }

  async signInWithEmailAndPassword(email: string, password: string): Promise<any> {
    try {
      const url = `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${process.env.PERSONAL_FIREBASE_API_KEY}`;

      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          password,
          returnSecureToken: true,
        }),
      });

      if (!response.ok) {
        const errorResponse = await response.json();
        this.logger.warn('Firebase Auth error response', JSON.stringify(errorResponse));
        throw new BadRequestException('Invalid email or password.');
      }

      return await response.json();
    } catch (error) {
      this.logger.error('Sign-in error', error.stack);
      throw error;
    }
  }

  async createCustomToken(uid: string): Promise<string> {
    return admin.auth().createCustomToken(uid);
  }

  async verifyToken(token: string): Promise<admin.auth.DecodedIdToken> {
    return this.firebaseApp.auth().verifyIdToken(token);
  }

  async createUser(email: string, password: string): Promise<admin.auth.UserRecord> {
    return admin.auth().createUser({ email, password });
  }

  async verifyGoogleToken(idToken: string) {
    try {
      return await admin.auth().verifyIdToken(idToken);
    } catch (error) {
      this.logger.error('Invalid Google token', error.stack);
      throw new BadRequestException('Invalid Google token');
    }
  }

  async updateUserPassword(uid: string, newPassword: string): Promise<void> {
    try {
      await admin.auth().updateUser(uid, { password: newPassword });
    } catch (error) {
      this.logger.error(`Error updating password for UID: ${uid}`, error.stack);
      throw new BadRequestException('Failed to update password');
    }
  }

  async deleteUser(uid: string): Promise<void> {
    try {
      await admin.auth().deleteUser(uid);
      this.logger.log(`Successfully deleted user with UID: ${uid}`);
    } catch (error) {
      this.logger.error(`Failed to delete user with UID: ${uid}`, error.stack);
      throw new BadRequestException('Failed to delete user');
    }
  }
}
