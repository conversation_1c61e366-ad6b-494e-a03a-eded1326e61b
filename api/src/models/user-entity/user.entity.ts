import { Is<PERSON><PERSON>, IsNotEmpty } from 'class-validator';
import {
  Column,
  Entity,
  OneToMany,
  ManyToOne,
  JoinColumn,
  PrimaryColumn,
  OneToOne,
} from 'typeorm';
import { RoleEntity, ROLE_VALUES } from './role.entity';
import { AccessTokenEntity } from './accessToken.entity';
import { TraineeProfileEntity } from '../profiles/trainee-profile.entity';
import { TrainerProfileEntity } from '../profiles/trainer-profile.entity';
import { FoodItemEntity } from '../meals';

@Entity('users')
export class UserEntity {
  @PrimaryColumn() // Use Firebase UID as the primary key
  id: string;

  @Column({ nullable: true })
  name: string; // optional

  @IsEmail()
  @Column({ unique: true, nullable: false })
  email: string;

  @Column({ nullable: true })
  mobileNumber: string;

  @Column({ nullable: true })
  passwordHash: string;

  @Column({ default: true })
  isActive: boolean;

  @Column()
  roleId: number; // Store only the role ID

  @Column({ nullable: true, default: false })
  isVerified: boolean; // Store only the role ID

  @ManyToOne(() => RoleEntity, { eager: false })
  @JoinColumn({ name: 'roleId' })
  role: RoleEntity;

  @OneToMany(() => AccessTokenEntity, (accessToken) => accessToken.user)
  accessToken: AccessTokenEntity[];

  @Column({ type: 'date', nullable: true })
  firstWorkoutDate: string | null;

  // Conditional relations based on role
  @OneToOne(() => TrainerProfileEntity, (profile) => profile.user, {
    nullable: true,
    cascade: true,
  })
  trainerProfile?: TrainerProfileEntity;

  @OneToOne(() => TraineeProfileEntity, (profile) => profile.user, {
    nullable: true,
    cascade: true,
  })
  traineeProfile?: TraineeProfileEntity;

  @OneToOne(() => FoodItemEntity, (foodItem) => foodItem.creator)
  foodItems: FoodItemEntity[];

  @Column({ type: 'boolean', default: false })
  isMacrosHidden: boolean;
}
