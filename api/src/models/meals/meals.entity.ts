import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinTable,
  JoinColumn,
} from 'typeorm';
import { UserEntity } from '../user-entity';
import { MealCategoryEntity } from './meals-categories.entity';
import { FoodItemEntity } from './food-items.entity';

@Entity('meals')
export class MealEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  traineeId: string;

  @Column({ nullable: true })
  createdById: string;

  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'traineeId' })
  trainee: UserEntity;



  @Column({ nullable: true })
  defaultServing: string;

  @Column({ nullable: true })
  maxServing: string;

  @Column({ nullable: true })
  minServing: string;

  @Column({ nullable: true })
  description: string;

  @Column('jsonb', { nullable: true })
  macroRange: {
    min: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
    max: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
  };

  @OneToMany(() => MealCategoryEntity, (category) => category.meal, {
    cascade: true,
    eager: true,
  })
  categories: MealCategoryEntity[];

  @Column('jsonb', { nullable: true })
  foodItemIds: string[];

  @ManyToMany(() => FoodItemEntity)
  @JoinTable({
    name: 'meal_food_items',
    joinColumn: { name: 'mealId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'foodItemId', referencedColumnName: 'id' },
  })
  foodItems: FoodItemEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
