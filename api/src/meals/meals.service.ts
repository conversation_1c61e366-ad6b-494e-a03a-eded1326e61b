import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import {
  MealCategoryEntity,
  MealEntity,
  MealOptionEntity,
  MealTemplateEntity,
} from 'src/models/meals';
import { UserEntity } from 'src/models/user-entity';
import { EntityManager, Repository } from 'typeorm';
import { FoodItemsService } from './food-items/food-items.service';
import { MacroRange } from 'src/utils/interfaces';
import { CreateMealDto, UpdateMealDto } from './dto';
import { CreateMealCategoryDto } from './meal-categories/dto';

@Injectable()
export class MealsService {
  constructor(
    @InjectRepository(MealEntity)
    private mealsRepository: Repository<MealEntity>,

    @InjectRepository(MealTemplateEntity)
    private mealTemplateRepository: Repository<MealTemplateEntity>,

    @InjectRepository(MealCategoryEntity)
    private mealCategoriesRepository: Repository<MealCategoryEntity>,

    @InjectRepository(UserEntity)
    private userRespository: Repository<UserEntity>,

    private foodItemsService: FoodItemsService,

    @InjectEntityManager()
    private entityManager: EntityManager,
  ) {}

  async findAll(userId?: string): Promise<MealEntity[]> {
    const find_all_meals_for_trainee= await this.mealsRepository.find({
      where: userId ? { createdById: userId } : {},
      relations: [
        'categories',
        'categories.options',
        'categories.options.food',
        'foodItem',
      ],
    });


    console.log(`data after find_all_meals_for_trainee: ${userId}`,JSON.stringify(find_all_meals_for_trainee,null,2))

    return find_all_meals_for_trainee;

  }

  async findOne(id: string): Promise<MealEntity> {
    return this.mealsRepository.findOne({
      where: { id },
      relations: [
        'categories',
        'categories.options',
        'categories.options.food',
        'foodItem',
      ],
    });
  }

  async create(mealDto: CreateMealDto, user: UserEntity): Promise<MealEntity> {
    // If foodItemIds are provided, verify each one exists
    if (mealDto.foodItemIds) {
      const foodItems = await Promise.all(
        mealDto.foodItemIds.map((id) => this.foodItemsService.findOne(id)),
      );
      const notFoundItems = mealDto.foodItemIds.filter(
        (id, index) => !foodItems[index],
      );
      if (notFoundItems.length > 0) {
        throw new NotFoundException(
          `Food items not found: ${notFoundItems.join(', ')}`,
        );
      }
    }

    const newMeal = this.mealsRepository.create({
      ...mealDto,
      createdById: user.id,
      traineeId: mealDto.traineeId,
      foodItemIds: mealDto.foodItemIds,
    });

    return this.mealsRepository.save(newMeal);
  }

  async remove(id: string): Promise<void> {
    await this.mealsRepository.delete(id);
  }

  async calculateMealMacros(mealId: string): Promise<MacroRange> {
    const meal = await this.findOne(mealId);
    let minMacros = { protein: 0, carbs: 0, fats: 0, calories: 0 };
    let maxMacros = { protein: 0, carbs: 0, fats: 0, calories: 0 };

    for (const category of meal.categories) {
      for (const option of category.options) {
        const macros = this.foodItemsService.calculateMacros(
          option.food,
          option.amount,
        );

        if (
          category.options.indexOf(option) === 0 ||
          macros.calories < minMacros.calories
        ) {
          minMacros.protein += macros.protein;
          minMacros.carbs += macros.carbs;
          minMacros.fats += macros.fats;
          minMacros.calories += macros.calories;
        }

        maxMacros.protein += macros.protein;
        maxMacros.carbs += macros.carbs;
        maxMacros.fats += macros.fats;
        maxMacros.calories += macros.calories;
      }
    }

    return { min: minMacros, max: maxMacros };
  }

  async findByTrainee(traineeId: string): Promise<MealEntity[]> {
    if (!traineeId) {
      throw new NotFoundException('Trainee ID is required');
    }

    // Fetch meals with foodItemIds
    const meals = await this.mealsRepository.find({
      where: { traineeId },
      relations: [
        'categories',
        'categories.options',
        'categories.options.food',
      ],
      order: { createdAt: 'ASC' },
    });
    // Manually fetch food items for each meal
    for (const meal of meals) {
      if (meal.foodItemIds && meal.foodItemIds.length > 0) {
        meal.foodItems = await this.foodItemsService.findByIds(
          meal.foodItemIds,
        );
      } else {
        meal.foodItems = [];
      }
    }

    return meals;
  }

  async update(id: string, mealDto: UpdateMealDto): Promise<MealEntity> {
    // First, fetch the existing meal
    const meal = await this.mealsRepository.findOne({
      where: { id },
      relations: ['categories', 'categories.options', 'foodItems'],
    });

    if (!meal) {
      throw new NotFoundException(`Meal with ID ${id} not found`);
    }

    // Update simple properties
    if (mealDto.name) {
      meal.name = mealDto.name;
    }

    if (mealDto.description !== undefined) {
      meal.description = mealDto.description;
    }

    if (mealDto.macroRange !== undefined) {
      meal.macroRange = mealDto.macroRange;
    }

    // Handle categories if provided
    if (mealDto.categories && mealDto.categories.length > 0) {
      // Delete existing categories
      if (meal.categories && meal.categories.length > 0) {
        await this.mealCategoriesRepository.remove(meal.categories);
      }

      // Create new categories
      meal.categories = await this.createCategories(mealDto.categories);
    }

    // Handle food items if needed
    if (mealDto.foodItemIds) {
      const foodItems = await this.foodItemsService.findByIds(
        mealDto.foodItemIds,
      );

      meal.foodItems = foodItems;
    }

    // Save the meal

    const savedMeal = await this.mealsRepository.save(meal);

    // Return the meal without circular references
    const updatedMeal = this.mealsRepository.findOne({
      where: { id: savedMeal.id },
      relations: [
        'categories',
        'categories.options',
        'categories.options.food',
      ],
    });

    return updatedMeal;
  }

  private async createCategories(
    categoriesDto: CreateMealCategoryDto[],
  ): Promise<MealCategoryEntity[]> {
    const newCategories: MealCategoryEntity[] = [];

    for (const categoryDto of categoriesDto) {
      const category = new MealCategoryEntity();
      category.name = categoryDto.name;

      // Save the category first without the meal reference
      const savedCategory = await this.mealCategoriesRepository.save(category);

      // Handle options if they exist
      if (categoryDto.options && categoryDto.options.length > 0) {
        // We need to create a repository for MealOptionEntity
        const optionRepository =
          this.entityManager.getRepository(MealOptionEntity);

        // Create options for this category
        const options = [];

        for (const optionDto of categoryDto.options) {
          const option = new MealOptionEntity();
          option.foodId = optionDto.foodId;
          option.amount = optionDto.amount;
          option.category = savedCategory;

          // Save the option
          const savedOption = await optionRepository.save(option);
          options.push(savedOption);
        }

        // Assign options to the category
        savedCategory.options = options;
        await this.mealCategoriesRepository.save(savedCategory);
      }

      newCategories.push(savedCategory);
    }

    return newCategories;
  }

  async createMealsFromTemplate(
    templateId: string,
    traineeId: string,
    trainerId: string,
  ) {
    if (!trainerId) {
      throw new BadRequestException('Please provide trainer Id.');
    }

    if (!traineeId) {
      throw new BadRequestException('Please provide trainee Id.');
    }

    const template = await this.mealTemplateRepository.findOneBy({
      id: templateId,
      trainerId: trainerId,
    });

    if (!template) throw new NotFoundException('Template not found');

    const trainee = await this.userRespository.findOneBy({ id: traineeId });

    if (!trainee) throw new NotFoundException('Trainee not found');

    const createdMeals = await Promise.all(
      template.templateData.map(async (mealData) => {
        const foodIds = mealData.categories.flatMap((cat) =>
          cat.options.map((opt) => opt.foodId),
        );

        const foodItems = await Promise.all(
          foodIds.map((id) => this.foodItemsService.findOne(id)),
        );
        const notFoundItems = foodIds.filter((id, index) => !foodItems[index]);

        if (notFoundItems.length > 0) {
          throw new NotFoundException(
            `Food items not found: ${notFoundItems.join(', ')}`,
          );
        }

        const newMeal = this.mealsRepository.create({
          name: mealData.meal_name,
          createdById: trainerId,
          traineeId,
          macroRange: mealData.macroRange,
          categories: mealData.categories,
          foodItemIds: foodIds,
        });

        const createdMeal = await this.mealsRepository.save(newMeal);

        return createdMeal;
      }),
    );

    return createdMeals;
  }

  async updateTraineeMacrosVisibility(traineeId: string): Promise<UserEntity> {
    const trainee = await this.userRespository.findOne({
      where: { id: traineeId },
    });

    if (!trainee) {
      throw new BadRequestException('Trainee not found');
    }

    // Toggle the isMacrosHidden setting
    trainee.isMacrosHidden = !trainee.isMacrosHidden;
    return this.userRespository.save(trainee);
  }
}
