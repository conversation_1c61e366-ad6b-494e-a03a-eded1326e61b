import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { FirebaseService } from 'src/third-party/firebase/firebase-authentication.service';
import { AccessTokenService } from './access-token/access-token.service';
import {
  AccessTokenEntity,
  ROLE_VALUES,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RegisterDto } from './dto';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<RoleEntity>,

    @InjectRepository(AccessTokenEntity)
    private readonly accessTokenRepo: Repository<AccessTokenEntity>,

    private readonly firebaseService: FirebaseService,
    private readonly accessTokenService: AccessTokenService,
  ) {}

  async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
  }

  async register(userData: RegisterDto) {
    try {
      const { email, password, role, name, mobileNumber } = userData;
      const passwordHash = await this.hashPassword(password);
      const firebaseUser = await this.firebaseService.createUser(
        email,
        password,
      );
      const roleEnumValue =
        ROLE_VALUES[role.toUpperCase() as keyof typeof ROLE_VALUES];
      if (!roleEnumValue) {
        throw new BadRequestException(`Invalid role provided: ${role}`);
      }
      const roleEntity = await this.roleRepo.findOne({
        where: { value: roleEnumValue },
      });
      if (!roleEntity) {
        throw new BadRequestException(`
          Role not found in the database: ${roleEnumValue}.`);
      }
      const newUser = this.userRepo.create({
        id: firebaseUser.uid,
        email: firebaseUser.email,
        passwordHash,
        name: name,
        mobileNumber: mobileNumber,
        roleId: roleEntity.id,
        isActive: true,
      });
      await this.userRepo.save(newUser);
      return { message: 'User registered successfully', user: newUser };
    } catch (error) {
      throw new BadRequestException(
        'User registration failed. Please try again.',
      );
    }
  }

  async login(email: string, password: string, clientPlatform?: string) {
    try {
      // Firebase authentication
      const firebaseUser =
        await this.firebaseService.signInWithEmailAndPassword(email, password);
      if (!firebaseUser) {
        throw new UnauthorizedException('Invalid email or password.');
      }
      // Find user in database
      const user = await this.userRepo.findOne({ where: { email } });
      if (!user) {
        throw new BadRequestException('User not found');
      }
      // Fetch role
      const roleEntity = await this.roleRepo.findOne({
        where: { id: user.roleId },
      });
      if (!roleEntity) {
        throw new BadRequestException(`Role not found for ID: ${user.roleId}`);
      }
      // Check mobile platform restrictions
      if (clientPlatform === 'mobile') {
        // Only allow trainee role to login from mobile
        if (roleEntity.value !== ROLE_VALUES.TRAINEE) {
          throw new ForbiddenException(
            'Only trainee users can login from mobile app',
          );
        }
      }
      // Get Firebase refresh token (from the response of Firebase authentication)
      const firebaseRefreshToken = firebaseUser.refreshToken;
      // Generate JWT token
      const jwtToken = this.accessTokenService.generateToken({
        email: user.email,
        id: user.id,
        role: user.roleId,
      });
      // Create and save access token entity with Firebase refresh token
      const accessTokenEntity = this.accessTokenRepo.create({
        accessToken: jwtToken,
        firebaseRefreshToken, // Save refresh token
        user: user,
        expiry: this.calculateTokenExpiration(),
        isDeleted: false,
      });
      await this.accessTokenRepo.save(accessTokenEntity);
      return {
        message: 'Login successful',
        jwtToken,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          isActive: user.isActive,
          role: {
            id: user.roleId,
            name: roleEntity.value,
          },
        },
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async googleLogin(idToken: string, clientPlatform?: string) {
    try {
      // Verify Google token using Firebase
      const decodedToken =
        await this.firebaseService.verifyGoogleToken(idToken);
      // Extract user information from Google token
      const { uid, email, name, picture } = decodedToken;
      if (!email) {
        throw new BadRequestException('Email is required for authentication');
      }
      // Check if user exists in the database
      let user = await this.userRepo.findOne({ where: { email } });
      if (!user) {
        const defaultRole = await this.roleRepo.findOne({
          where: { value: ROLE_VALUES.TRAINEE },
        });
        if (!defaultRole) {
          throw new BadRequestException('Default role not found');
        }
        // Create new user if not exists
        user = this.userRepo.create({
          id: uid,
          email,
          passwordHash: null, // No password needed for Google login
          roleId: defaultRole.id,
          isActive: true,
          // Mobile number will be null initially for Google login users
        });
        await this.userRepo.save(user);
      }
      // Fetch role for platform validation
      const roleEntity = await this.roleRepo.findOne({
        where: { id: user.roleId },
      });
      if (!roleEntity) {
        throw new BadRequestException(`Role not found for ID: ${user.roleId}`);
      }
      // Check mobile platform restrictions
      if (clientPlatform === 'mobile') {
        // Only allow trainee role to login from mobile
        if (roleEntity.value !== ROLE_VALUES.TRAINEE) {
          throw new ForbiddenException(
            'Only trainee users can login from mobile app',
          );
        }
      }
      // Generate JWT token
      const jwtToken = this.accessTokenService.generateToken({
        email: user.email,
        id: user.id,
        role: user.roleId,
      });
      // Store JWT token
      const accessTokenEntity = this.accessTokenRepo.create({
        accessToken: jwtToken,
        user: user,
        expiry: this.calculateTokenExpiration(),
        isDeleted: false,
      });
      await this.accessTokenRepo.save(accessTokenEntity);
      return {
        message: 'Google login successful',
        jwtToken,
        user: {
          id: user.id,
          email: user.email,
          name,
          picture,
          isActive: user.isActive,
          role: user.roleId,
        },
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async findOne(email: string): Promise<UserEntity | null> {
    try {
      const user = await this.userRepo.findOne({ where: { email } });
      return user || null;
    } catch (error) {
      throw new BadRequestException('Error retrieving user');
    }
  }

  async findById(userId: string): Promise<UserEntity | null> {
    try {
      const user = await this.userRepo.findOne({ where: { id: userId } });
      return user || null;
    } catch (error) {
      throw new BadRequestException('Error retrieving user by ID');
    }
  }

  async findByMobileNumber(mobileNumber: string): Promise<UserEntity | null> {
    try {
      // Normalize the mobile number by removing 'whatsapp:' prefix if present
      const normalizedNumber = mobileNumber.replace('whatsapp:', '');
      // Find user by mobile number
      const user = await this.userRepo.findOne({
        where: { mobileNumber: normalizedNumber },
        relations: ['role'], // Include role information
      });
      return user || null;
    } catch (error) {
      throw new BadRequestException('Error retrieving user by mobile number');
    }
  }

  // Calculate token expiration (e.g., 30 days from now)
  private calculateTokenExpiration(): Date {
    const expiresIn = 30 * 24 * 60 * 60 * 1000; // 30 days
    return new Date(Date.now() + expiresIn);
  }

  async refreshToken(oldToken: string) {
    try {
      // Find the existing access token
      const existingTokenEntity = await this.accessTokenRepo.findOne({
        where: { accessToken: oldToken, isDeleted: false },
        relations: ['user'],
      });
      if (!existingTokenEntity || existingTokenEntity.expiry < new Date()) {
        throw new UnauthorizedException('Invalid or expired token');
      }
      // Mark old token as deleted
      existingTokenEntity.isDeleted = true;
      await this.accessTokenRepo.save(existingTokenEntity);
      // Generate new token
      const newJwtToken = this.accessTokenService.generateToken({
        email: existingTokenEntity.user.email,
        id: existingTokenEntity.user.id,
        role: existingTokenEntity.user.roleId,
      });
      // Create and save new access token entity
      const newAccessTokenEntity = this.accessTokenRepo.create({
        accessToken: newJwtToken,
        user: existingTokenEntity.user,
        expiry: this.calculateTokenExpiration(),
        isDeleted: false,
      });
      await this.accessTokenRepo.save(newAccessTokenEntity);
      return {
        jwtToken: newJwtToken,
      };
    } catch (error) {
      throw new UnauthorizedException('Token refresh failed');
    }
  }

  async logout(token: string) {
    try {
      // Find and mark the token as deleted
      const tokenEntity = await this.accessTokenRepo.findOne({
        where: { accessToken: token, isDeleted: false },
      });
      if (tokenEntity) {
        tokenEntity.isDeleted = true;
        await this.accessTokenRepo.save(tokenEntity);
      }
      return { message: 'Logout successful' };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
