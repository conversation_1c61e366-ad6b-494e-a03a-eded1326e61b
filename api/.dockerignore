# Node modules
node_modules

# TypeScript build output
dist
build

# Logs and temporary files
npm-debug.log
yarn-error.log
*.log
*.tsbuildinfo

# Environment files (if managed outside of Docker)
.env

# IDE and editor files
.vscode
.idea
*.swp

# Test and coverage files
coverage
test
*.spec.ts

# Dockerfile and docker-compose.yml (optional to exclude if you want them in the image)
docker-compose.yml
Dockerfile

# Other system files
.DS_Store
Thumbs.db