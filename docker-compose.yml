version: '3.9'

services:
  agents:
    build:
      context: ./agents
      dockerfile: Dockerfile
    env_file:
      - ./agents/.env
    ports:
      - "${PORT:-8000}:8000"
    depends_on:
      - redis

  redis:
    image: redis:7
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: redis-server --save 60 1 --loglevel warning
    # Optional volume if you want persistence
    # volumes:
    #   - redis_data:/data

  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    env_file:
      - ./api/.env
    ports:
      - "${PORT:-4000}:4000"
    depends_on:
      - postgres
    networks:
      - api_network
  
  postgres:
    image: postgres:13
    container_name: postgres
    env_file:
      - ./api/.env
    ports:
      - "${PORT:-5432}:5432"
    networks:
      - api_network

  new-frontend:
    build:
      context: ./new-frontend
      dockerfile: Dockerfile
    env_file:
      - ./new-frontend/.env
    ports:
      - "${PORT:-3000}:80"  # Exposing the frontend on port 3000
    depends_on:
      - api        # Ensure frontend waits for API to be up (if needed)
    networks:
      - api_network

networks:
  api_network:
    driver: bridge