export interface MacroNutrients {
  protein: number;
  carbs: number;
  fats: number;
  calories: number;
}

export interface MacroRange {
  min: MacroNutrients;
  max: MacroNutrients;
}

export interface FoodItem {
  id: string;
  name: string;
  category: string;
  defaultServing: number;
  macrosPer100g: MacroNutrients;
}

export interface MealCategory {
  name: string;
  options: {
    foodId: string;
    amount: number;
  }[];
}

export interface MealWithFood {
  id: string;
  name: string;
  categories: MealCategory[];
  macroRange: MacroRange;
  foodItems?: FoodItem[];
}

export interface UserMealPlanProps {
  userId: string;
}

// API Meal Type - moved from meals.api.ts
export interface MealType {
  name: string;
  description: string;
  mealTime: string;
  traineeId: string;
  isFeatured: boolean;
  macroRange: {
    min: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
    max: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
  };
  categories: {
    id: string;
    name: string;
  }[];
  foodItemIds: string[];
}
