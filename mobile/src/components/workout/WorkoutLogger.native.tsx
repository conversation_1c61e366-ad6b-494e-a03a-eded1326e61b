import React, { useEffect, useState, useCallback } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
} from "react-native";
import {
  <PERSON><PERSON><PERSON>,
  Timer,
  SkipForward,
  Play,
  Square,
  FastForward,
  CheckCircle,
  CircleCheckBig,
  CircleHelp,
} from "lucide-react-native";
import { useWorkout } from "../../contexts/WorkoutContext";
import { useWorkoutLog } from "../../contexts/WorkoutLogContext";
import {
  WorkoutLog,
  WorkoutLoggerProps,
  Exercise,
  ExerciseLog,
} from "../../types/workout";
import { Picker } from "@react-native-picker/picker";
import { trainingPlansApi } from "../../api/training.api";
import { workoutLogsApi } from "../../api/workout-logs.api";
import { workoutSetsApi } from "../../api/workout-sets.api";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useTheme } from "../../contexts/ThemeContext";
import { createStyles } from "../../style/WorkoutLogger.style";
import { useTranslation } from "react-i18next";
import {
  GestureHandlerRootView,
  RefreshControl,
  ScrollView,
} from "react-native-gesture-handler";

export const WorkoutLogger: React.FC<WorkoutLoggerProps> = ({
  userId,
  onWorkoutStart,
  onWorkoutEnd,
  onRestStart,
  onPauseToggle,
  restTimer,
  isPaused,
  autoFinish,
  setAutoFinish,
}) => {
  const {
    isWorkoutStarted,
    setIsWorkoutStarted,
    selectedDay,
    setSelectedDay,
    currentExerciseIndex,
    setCurrentExerciseIndex,
    activeExercise,
    setActiveExercise,
    weight,
    setWeight,
    reps,
    setReps,
    appTrainingPlan,
    setAppTrainingPlan,
    selectedPlanId,
    setSelectedPlanId,
    workoutLog,
    setWorkoutLog,
  } = useWorkout();

  const { t } = useTranslation();

  const { addWorkoutLog } = useWorkoutLog();
  const [exerciseLogs, setExerciseLogs] = useState<ExerciseLog[]>([]);
  const [showFinishConfirmation, setShowFinishConfirmation] = useState(false);
  const { isDark } = useTheme();
  const styles = createStyles(isDark ? "dark" : "light");

  // Track workout timing
  const [workoutStartTime, setWorkoutStartTime] = useState<Date | null>(null);
  const [totalPausedTime, setTotalPausedTime] = useState<number>(0);
  const [pauseStartTime, setPauseStartTime] = useState<Date | null>(null);
  const [workoutLogId, setWorkoutLogId] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const [isWorkoutInfoOpen, setIsWorkoutInfoOpen] = useState(false);

  // Auto-clear error message after 5 seconds
  useEffect(() => {
    let errorTimeout: NodeJS.Timeout;
    if (errorMessage) {
      errorTimeout = setTimeout(() => {
        setErrorMessage(null);
      }, 5000); // 5 seconds
    }
    return () => {
      if (errorTimeout) clearTimeout(errorTimeout);
    };
  }, [errorMessage]);

  // Add refresh control state
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Create a function to fetch training plans that doesn't check appTrainingPlan.length
  const fetchTrainingPlans = useCallback(
    async (forceRefresh = false) => {
      // Skip refresh if already in a workout
      if (isWorkoutStarted && !forceRefresh) return;

      setIsLoading(true);
      try {
        // Get plans from API
        const plans = await trainingPlansApi.getById(userId);

        // Update state with fresh data
        setAppTrainingPlan(plans);

        // Cache the training plans
        try {
          await AsyncStorage.setItem(
            `training_plans_${userId}`,
            JSON.stringify(plans)
          );
          // Store last refresh timestamp
          await AsyncStorage.setItem(
            `training_plans_last_refresh_${userId}`,
            new Date().toISOString()
          );
        } catch (cacheError) {
          console.error("Error caching training plans:", cacheError);
        }
      } catch (error) {
        console.error("Error fetching training plans:", error);

        // Try to load from cache if API fails
        try {
          const cachedPlans = await AsyncStorage.getItem(
            `training_plans_${userId}`
          );
          if (cachedPlans) {
            setAppTrainingPlan(JSON.parse(cachedPlans));
          }
        } catch (cacheError) {
          console.error("Error loading cached training plans:", cacheError);
        }
      } finally {
        setIsLoading(false);
        setRefreshing(false);
      }
    },
    [userId, isWorkoutStarted, setAppTrainingPlan]
  );

  const clearTrainingPlansCache = async () => {
    try {
      await AsyncStorage.removeItem(`training_plans_${userId}`);
      await AsyncStorage.removeItem(`training_plans_last_refresh_${userId}`);
    } catch (error) {
      console.error("Error clearing training plans cache:", error);
    }
  };

  // Call this function when appropriate, such as when the component mounts
  useEffect(() => {
    clearTrainingPlansCache();
  }, [userId]);

  // The pull-to-refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchTrainingPlans(true);
    setLastRefresh(new Date());
  }, [fetchTrainingPlans]);

  // Initial load
  useEffect(() => {
    const initialLoad = async () => {
      // Check when we last refreshed the training plans
      try {
        const lastRefreshStr = await AsyncStorage.getItem(
          `training_plans_last_refresh_${userId}`
        );

        // If we have cached plans, load them first for faster UI response
        const cachedPlans = await AsyncStorage.getItem(
          `training_plans_${userId}`
        );
        if (cachedPlans) {
          setAppTrainingPlan(JSON.parse(cachedPlans));
        }

        // Check if cache should be refreshed (30 minutes threshold)
        const shouldRefresh =
          !lastRefreshStr ||
          new Date().getTime() - new Date(lastRefreshStr).getTime() >
            30 * 60 * 1000;

        if (shouldRefresh || appTrainingPlan.length === 0) {
          // Fetch fresh data if cache is stale or empty
          fetchTrainingPlans(true);
        }
      } catch (error) {
        console.error("Error during initial load:", error);
        // Fall back to direct API fetch
        fetchTrainingPlans(true);
      }
    };

    initialLoad();
  }, [userId, fetchTrainingPlans]);

  // Restore workout state from persistent storage if available
  useEffect(() => {
    async function restoreWorkoutState() {
      try {
        // Attempt to restore workout timing data
        const workoutTimingData = await AsyncStorage.getItem(
          `workout_timing_${userId}`
        );
        if (workoutTimingData) {
          const parsedData = JSON.parse(workoutTimingData);
          if (parsedData.workoutStartTime) {
            setWorkoutStartTime(new Date(parsedData.workoutStartTime));
          }
          if (parsedData.totalPausedTime) {
            setTotalPausedTime(parsedData.totalPausedTime);
          }
          if (parsedData.workoutLogId) {
            setWorkoutLogId(parsedData.workoutLogId);
          }
        }

        // Restore exercise logs if available
        const logsData = await AsyncStorage.getItem(`exercise_logs_${userId}`);
        if (logsData) {
          setExerciseLogs(JSON.parse(logsData));
        }
      } catch (error) {
        console.error("Error restoring workout state:", error);
      }
    }

    restoreWorkoutState();
  }, [userId]);

  // Save important state data when it changes
  useEffect(() => {
    async function saveWorkoutTiming() {
      if (isWorkoutStarted && workoutStartTime) {
        try {
          await AsyncStorage.setItem(
            `workout_timing_${userId}`,
            JSON.stringify({
              workoutStartTime: workoutStartTime.toISOString(),
              totalPausedTime,
              workoutLogId,
            })
          );
        } catch (error) {
          console.error("Error saving workout timing:", error);
        }
      }
    }
    saveWorkoutTiming();
  }, [
    isWorkoutStarted,
    workoutStartTime,
    totalPausedTime,
    workoutLogId,
    userId,
  ]);

  // Save exercise logs when they change
  useEffect(() => {
    async function saveExerciseLogs() {
      if (exerciseLogs.length > 0) {
        try {
          await AsyncStorage.setItem(
            `exercise_logs_${userId}`,
            JSON.stringify(exerciseLogs)
          );
        } catch (error) {
          console.error("Error saving exercise logs:", error);
        }
      }
    }
    saveExerciseLogs();
  }, [exerciseLogs, userId]);

  useEffect(() => {
    if (isWorkoutStarted && selectedPlanId && !activeExercise) {
      // If workout is started but activeExercise is missing, attempt to restore it
      restoreActiveWorkout();
    }
  }, [isWorkoutStarted, selectedPlanId, activeExercise]);

  // Handle pause/resume timing
  useEffect(() => {
    if (isPaused && !pauseStartTime) {
      setPauseStartTime(new Date());
    } else if (!isPaused && pauseStartTime) {
      const now = new Date();
      const pauseDuration = Math.floor(
        (now.getTime() - pauseStartTime.getTime()) / 1000
      );
      setTotalPausedTime((prev) => prev + pauseDuration);
      setPauseStartTime(null);
    }
  }, [isPaused]);

  const handleDayChange = (selectedDayValue: string) => {
    setSelectedDay(selectedDayValue);
    const selectedPlan = appTrainingPlan.find(
      (day) => day.day === selectedDayValue
    );
    const planId = selectedPlan ? selectedPlan.id : null;
    setSelectedPlanId(planId);
  };

  // New function to restore active workout
  const restoreActiveWorkout = async () => {
    setIsLoading(true);
    try {
      // Only get workout data if we don't already have it
      if (!workoutLog) {
        // Try to get from storage first
        const savedWorkoutLog = await AsyncStorage.getItem(
          `workout_log_${selectedPlanId}`
        );
        if (savedWorkoutLog) {
          const parsedWorkoutLog = JSON.parse(savedWorkoutLog);
          setWorkoutLog(parsedWorkoutLog);

          // Find the current exercise based on stored data
          const workout = appTrainingPlan.find(
            (day) => day.day === selectedDay
          );
          if (
            workout &&
            workout.exercises &&
            workout.exercises.length > currentExerciseIndex
          ) {
            const currentExercise = workout.exercises[currentExerciseIndex];

            // Map workoutLog exercises for fast lookup
            const workoutLogMap = new Map<string, string>(
              parsedWorkoutLog.exercises.map((exercise: any) => [
                exercise.name,
                exercise.id,
              ])
            );

            const exerciseId = workoutLogMap.get(currentExercise.name) ?? "";

            // Recreate activeExercise from stored data
            setActiveExercise({
              id: exerciseId,
              exercise_id: currentExercise.id,
              name: currentExercise.name,
              instructions: currentExercise.instructions,
              targetReps: currentExercise.reps,
              rest: currentExercise.rest,
              sets: Array(parseInt(currentExercise.sets)).fill({
                weight: 0,
                reps: 0,
                completed: false,
              }),
              currentSet: 0,
            });
          }
        } else {
          // If not in storage, try to get from API
          const res = await trainingPlansApi.getByTrainingId(selectedPlanId);

          if (res && res.exercises && res.exercises.length > 0) {
            // We need to recreate the workout log
            const workoutLogRes = await workoutLogsApi.create({
              trainingPlanId: res.id,
              date: new Date().toISOString().split("T")[0],
            });

            setWorkoutLogId(workoutLogRes.id);
            setWorkoutLog(workoutLogRes);

            // Cache the workout log
            await AsyncStorage.setItem(
              `workout_log_${selectedPlanId}`,
              JSON.stringify(workoutLogRes)
            );

            // Create a Map for fast lookup
            const workoutLogMap = new Map(
              workoutLogRes.exercises.map((exercise: any) => [
                exercise.name,
                exercise.id,
              ])
            );

            // Assign correct `id` for each exercise
            const updatedExercises = res.exercises.map((exercise: Exercise) => {
              const exerciseId = workoutLogMap.get(exercise.name) || "";

              return {
                exercise_id: exercise.id,
                id: exerciseId,
                name: exercise.name,
                instructions: exercise.instructions,
                reps: exercise.reps,
                rest: exercise.rest,
                sets: exercise.sets,
              };
            });

            setAppTrainingPlan((prevPlans: any) => {
              const otherPlans = prevPlans.filter(
                (plan: any) => plan.id !== res.id
              );
              return [...otherPlans, { ...res, exercises: updatedExercises }];
            });

            if (updatedExercises.length > 0) {
              const firstExercise = updatedExercises[0];

              setActiveExercise({
                id: firstExercise.id,
                exercise_id: firstExercise.exercise_id,
                name: firstExercise.name,
                instructions: firstExercise.instructions,
                targetReps: firstExercise.reps,
                rest: firstExercise.rest,
                sets: Array(parseInt(firstExercise.sets)).fill({
                  weight: 0,
                  reps: 0,
                  completed: false,
                }),
                currentSet: 0,
              });
            }
          }
        }
      }
    } catch (error) {
      console.error("Error restoring active workout:", error);
      setErrorMessage(t("workoutLogger.errors.restoreWorkoutError"));
    } finally {
      setIsLoading(false);
    }
  };

  const backendToFrontendErrorMap: Record<string, string> = {
    "Only trainees can log workouts.": t("workoutLogger.errors.onlyTrainees"),
    "You've already completed a workout today.": t(
      "workoutLogger.errors.alreadyCompleted"
    ),
    "Trainee does not have access to this training plan.": t(
      "workoutLogger.errors.noAccessToPlan"
    ),
  };

  const getWorkoutLogError = (backendMessage: string) => {
    return (
      backendToFrontendErrorMap[backendMessage] ||
      t("workoutLogger.errors.unexpectedError")
    );
  };

  const startWorkout = async () => {
    setIsWorkoutInfoOpen(false);

    if (!selectedPlanId) {
      console.error("Error: No selected training plan.");
      return;
    }

    setIsLoading(true);
    setAutoFinish(false);
    setWorkoutStartTime(new Date());
    setTotalPausedTime(0);
    setPauseStartTime(null);

    try {
      const res = await trainingPlansApi.getByTrainingId(selectedPlanId);

      if (!res || !res.exercises.length) {
        console.error("Error: No exercises found in training plan.");
        setIsLoading(false);
        return;
      }

      const workoutLogRes = await workoutLogsApi.create({
        trainingPlanId: res.id,
        date: new Date().toISOString().split("T")[0],
      });

      setWorkoutLogId(workoutLogRes.id);
      setWorkoutLog(workoutLogRes);

      await AsyncStorage.setItem(
        `workout_log_${selectedPlanId}`,
        JSON.stringify(workoutLogRes)
      );

      // Create a Map for fast lookup
      const workoutLogMap = new Map(
        workoutLogRes.exercises.map((exercise: any) => [
          exercise.name,
          exercise.id,
        ])
      );

      // Assign correct `id` for each exercise
      const updatedExercises = res.exercises.map((exercise: Exercise) => {
        const exerciseId = workoutLogMap.get(exercise.name) || "";

        return {
          exercise_id: exercise.id,
          id: exerciseId,
          name: exercise.name,
          instructions: exercise.instructions,
          reps: exercise.reps,
          rest: exercise.rest,
          sets: exercise.sets,
        };
      });

      setAppTrainingPlan((prevPlans: any) => {
        const otherPlans = prevPlans.filter((plan: any) => plan.id !== res.id);
        return [...otherPlans, { ...res, exercises: updatedExercises }];
      });

      // Cache updated training plans
      await AsyncStorage.setItem(
        `training_plans_${userId}`,
        JSON.stringify([
          ...appTrainingPlan.filter((plan: any) => plan.id !== res.id),
          { ...res, exercises: updatedExercises },
        ])
      );

      if (updatedExercises.length === 0) {
        console.error("Error: No exercises available to start.");
        setIsLoading(false);
        return;
      }

      const firstExercise = updatedExercises[0];

      setActiveExercise({
        id: firstExercise.id,
        exercise_id: firstExercise.exercise_id,
        name: firstExercise.name,
        instructions: firstExercise.instructions,
        targetReps: firstExercise.reps,
        rest: firstExercise.rest,
        sets: Array(parseInt(firstExercise.sets) || 1).fill({
          weight: 0,
          reps: 0,
          completed: false,
        }),
        currentSet: 0,
        targetSets: firstExercise.sets,
      });

      setCurrentExerciseIndex(0);
      setIsWorkoutStarted(true);
      setExerciseLogs([]);
      onWorkoutStart();
    } catch (error: any) {
      console.error("Error starting workout:", error);
      const errorMessage = error?.message || "";
      setErrorMessage(getWorkoutLogError(errorMessage));
    } finally {
      setIsLoading(false);
    }
  };

  const skipExercise = () => {
    const workout = appTrainingPlan.find((day) => day.day === selectedDay);
    if (!workout || currentExerciseIndex >= workout.exercises.length - 1)
      return;

    // Save current exercise logs if any sets were completed
    if (activeExercise && activeExercise.currentSet > 0) {
      const completedSets = activeExercise.sets
        .slice(0, activeExercise.currentSet)
        .map((set: any) => ({
          weight: set.weight,
          reps: set.reps,
        }));

      setExerciseLogs((prev) => [
        ...prev,
        {
          name: activeExercise.name,
          sets: completedSets,
        },
      ]);
    }

    const nextExercise = workout.exercises[currentExerciseIndex + 1];

    const sets = Array(parseInt(nextExercise.sets) || 1).fill({
      weight: 0,
      reps: 0,
      completed: false,
    });

    setActiveExercise({
      id: nextExercise.id,
      exercise_id: nextExercise.id,
      name: nextExercise.name,
      sets,
      currentSet: 0,
      rest: nextExercise.rest,
      instructions: nextExercise.instructions,
      targetReps: nextExercise.reps,
    });

    setCurrentExerciseIndex(currentExerciseIndex + 1);
    onRestStart(0);
    setWeight("");
    setReps("");
  };

  const skipRest = () => {
    onRestStart(0);
  };

  const togglePause = () => {
    onPauseToggle(!isPaused);
  };

  // Calculate workout duration in minutes
  const calculateWorkoutDuration = (): string => {
    if (!workoutStartTime)
      return t("workoutLogger.minutes").replace("{{value}}", "0");

    const now = new Date();
    let totalSeconds = Math.floor(
      (now.getTime() - workoutStartTime.getTime()) / 1000
    );

    // Subtract paused time
    let pausedSeconds = totalPausedTime;

    // Add current pause if workout is paused
    if (isPaused && pauseStartTime) {
      pausedSeconds += Math.floor(
        (now.getTime() - pauseStartTime.getTime()) / 1000
      );
    }

    // Subtract total paused time
    totalSeconds -= pausedSeconds;

    // Convert to minutes and round to the nearest minute
    const minutes = Math.max(1, Math.round(totalSeconds / 60));
    return t("workoutLogger.minutes").replace("{{value}}", `${minutes}`);
  };

  const handleFinishWorkoutClick = () => {
    setIsWorkoutInfoOpen(false);

    // Save current exercise logs if any sets were completed
    if (activeExercise && activeExercise.currentSet > 0) {
      const completedSets = activeExercise.sets
        .slice(0, activeExercise.currentSet)
        .map((set: any) => ({
          weight: set.weight,
          reps: set.reps,
        }));

      setExerciseLogs((prev) => [
        ...prev,
        {
          name: activeExercise.name,
          sets: completedSets,
        },
      ]);
    }

    setShowFinishConfirmation(true);
  };

  const confirmFinishWorkout = async () => {
    setIsLoading(true);
    try {
      const workout = appTrainingPlan.find((day) => day.day === selectedDay);

      if (workout) {
        // Calculate actual duration
        const duration = calculateWorkoutDuration();

        const workoutLogs: WorkoutLog = {
          id: workoutLogId || "",
          date: new Date().toISOString().split("T")[0],
          trainingPlanId: selectedPlanId || "",
          userId: userId,
          duration: duration,
          type: workout.focus,
          exercises: exerciseLogs,
        };

        // Try to save workout data
        await workoutLogsApi.complete(workoutLogId || "");
        await addWorkoutLog(workoutLogId || "", workoutLogs);

        // Clear persisted data
        await AsyncStorage.removeItem(`workout_timing_${userId}`);
        await AsyncStorage.removeItem(`exercise_logs_${userId}`);
        await AsyncStorage.removeItem(`workout_log_${selectedPlanId}`);
      }
    } catch (error) {
      console.error("Error saving workout log:", error);
      // Could add UI error notification here if desired
    } finally {
      // Reset all workout state regardless of success or failure
      setIsWorkoutStarted(false);
      setActiveExercise(null);
      setCurrentExerciseIndex(0);
      setWeight("");
      setReps("");
      setExerciseLogs([]);
      setShowFinishConfirmation(false);
      setAutoFinish(false);
      setWorkoutStartTime(null);
      setTotalPausedTime(0);
      setPauseStartTime(null);
      setIsLoading(false);
      onWorkoutEnd();
    }
  };

  const completeSet = async () => {
    if (!activeExercise || !weight || !reps || !workoutLog) {
      console.error("Missing required data for completing set.");
      return;
    }

    const workout = appTrainingPlan.find((day) => day.day === selectedDay);
    if (!workout) {
      console.error("Error: No matching training plan found.");
      return;
    }

    // console.log("set completed...");

    setIsLoading(true);
    const currentWeight = parseFloat(weight);
    const currentReps = parseInt(reps);

    // Map workoutLog exercises for fast lookup
    const workoutLogMap = new Map<string, string>(
      workoutLog.exercises.map((exercise: any) => [exercise.name, exercise.id])
    );

    // Ensure workoutExerciseId is a string
    const exerciseId = workoutLogMap.get(activeExercise.name) ?? "";

    const setData = {
      workoutExerciseId: exerciseId,
      setNumber: activeExercise.currentSet + 1,
      weight: currentWeight,
      reps: currentReps,
    };

    try {
      await workoutSetsApi.create(setData);
    } catch (error) {
      console.error("Error logging workout set:", error);
    } finally {
      setIsLoading(false);
    }

    // Update activeExercise state with completed set
    const newSets = [...activeExercise.sets];
    newSets[activeExercise.currentSet] = {
      weight: currentWeight,
      reps: currentReps,
      completed: true,
    };

    const restTime = parseInt(activeExercise.rest) || 60;
    onRestStart(restTime);

    if (
      (activeExercise.currentSet === 0 && activeExercise.targetSets === 1) ||
      activeExercise.currentSet === activeExercise.sets.length - 1
    ) {
      //   console.log("workout is progress....");

      setExerciseLogs((prev) => [
        ...prev,
        {
          name: activeExercise.name,
          sets: newSets,
        },
      ]);

      if (currentExerciseIndex < workout.exercises.length - 1) {
        // console.log("move to next exercise...");

        const nextExercise = workout.exercises[currentExerciseIndex + 1];

        // Ensure nextExerciseId is always a string
        const nextExerciseId = workoutLogMap.get(nextExercise.name) ?? "";

        setActiveExercise({
          id: nextExerciseId || "",
          exercise_id: nextExercise.id,
          name: nextExercise.name,
          sets: Array(parseInt(nextExercise.sets) || 1).fill({
            weight: 0,
            reps: 0,
            completed: false,
          }),
          currentSet: 0,
          rest: nextExercise.rest,
          instructions: nextExercise.instructions,
          targetReps: nextExercise.reps,
        });

        setCurrentExerciseIndex(currentExerciseIndex + 1);
        setWeight("");
        setReps("");
      } else {
        // console.log("finish workout...");

        setAutoFinish(true);
        handleFinishWorkoutClick();
      }
    } else {
      //   console.log("move to next set...");

      setActiveExercise({
        ...activeExercise,
        sets: newSets,
        currentSet: activeExercise.currentSet + 1,
      });

      setWeight("");
      setReps("");
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator
          size="large"
          color={isDark ? "#E9D5FF" : "#3B82F6"}
        />
        <Text style={styles.loadingText}>{t("workoutLogger.loading")}</Text>
      </View>
    );
  }

  if (!isWorkoutStarted) {
    return (
      <GestureHandlerRootView style={styles.container}>
        <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[isDark ? "#E9D5FF" : "#3B82F6"]} // Android
              tintColor={isDark ? "#E9D5FF" : "#3B82F6"} // iOS
            />
          }
          style={styles.workoutLogContainer}
        >
          {errorMessage && (
            <View style={styles.errorBanner}>
              <Text style={styles.errorText}>{errorMessage}</Text>
            </View>
          )}

          <View style={styles.titleContainer}>
            <Text style={styles.headerText}>{t("workoutLogger.title")}</Text>

            <View style={styles.headerIcon}>
              <Dumbbell
                color={isDark ? "#C4B5FD" : "#3B82F6"}
                width={24}
                height={24}
              />
            </View>
          </View>

          <View style={styles.selectDayContainer}>
            <Text style={styles.selectDayLabel}>
              {t("workoutLogger.selectDayLabel")}
            </Text>

            <View
              style={{
                borderWidth: 1,
                borderColor: isDark ? "#4B5563" : "black", // Purple-500 for dark mode
                borderRadius: 8,
                marginBottom: 16,
                backgroundColor: isDark ? "#374151" : "rgba(229, 231, 235, 1)", // Gray-700 for dark mode
                overflow: "hidden",
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.2,
                shadowRadius: 2,
                elevation: 2,
              }}
            >
              <Picker
                selectedValue={selectedDay}
                onValueChange={(itemValue) => handleDayChange(itemValue)}
                dropdownIconColor={isDark ? "#E9D5FF" : "black"}
                style={{ color: isDark ? "#E9D5FF" : undefined }}
              >
                <Picker.Item
                  label={t("workoutLogger.chooseDayOption")}
                  value=""
                />
                {appTrainingPlan.map((day) => (
                  <Picker.Item
                    key={day.id}
                    label={`${day.day} - ${day.focus}`}
                    value={day.day}
                  />
                ))}
              </Picker>
            </View>

            <TouchableOpacity
              style={styles.selectDayButton}
              onPress={startWorkout}
              disabled={!selectedDay}
            >
              <Text style={styles.selectDayButtonText}>
                {t("workoutLogger.startWorkout")}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </GestureHandlerRootView>
    );
  }

  if (!activeExercise) return null;

  const workout = appTrainingPlan.find((day) => day.day === selectedDay);
  const remainingSets = activeExercise.sets.length - activeExercise.currentSet;
  const remainingExercises = workout
    ? workout.exercises.length - currentExerciseIndex - 1
    : 0;

  return (
    <GestureHandlerRootView style={{ flexGrow: 1 }}>
      <ScrollView style={styles.container}>
        <View style={styles.workoutLogContainer}>
          <View style={styles.header}>
            <TouchableOpacity
              activeOpacity={0.5}
              style={styles.workoutInfoOpenButton}
              onPress={() => setIsWorkoutInfoOpen(!isWorkoutInfoOpen)}
            >
              <CircleHelp color={isDark ? "#E9D5FF" : "#3B82F6"} size={22} />
            </TouchableOpacity>

            {!autoFinish && restTimer > 0 && (
              <View style={styles.restTimerBlock}>
                <TouchableOpacity
                  onPress={skipRest}
                  style={styles.skipRestButton}
                >
                  <FastForward
                    color={isDark ? "#FCD34D" : "#B45309"}
                    size={19}
                  />
                  <Text style={styles.skipRestText}>
                    {t("workoutLogger.skipRest")}
                  </Text>
                </TouchableOpacity>

                <View style={styles.restTimerContainer}>
                  <Timer color={isDark ? "#FCD34D" : "#B45309"} size={16} />
                  <Text style={styles.restTimerText}>
                    {t("workoutLogger.restTimer", {
                      time: formatTime(restTimer),
                    })}
                  </Text>
                </View>
              </View>
            )}

            <View style={styles.titleContainer}>
              <Text style={styles.headerText}>{selectedDay}</Text>

              <View style={styles.headerIcon}>
                <Dumbbell
                  color={isDark ? "#C4B5FD" : "#3B82F6"}
                  width={24}
                  height={24}
                />
              </View>
            </View>
          </View>

          {isWorkoutInfoOpen && <WorkoutInfoContainer />}

          <View style={styles.exerciseContainer}>
            <View style={styles.exerciseHeader}>
              <View style={styles.exerciseButtons}>
                <TouchableOpacity
                  style={styles.finishButton}
                  onPress={handleFinishWorkoutClick}
                >
                  <CheckCircle
                    color={isDark ? "#FCA5A5" : "white"}
                    width={20}
                    height={20}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.exerciseButton,
                    autoFinish && styles.disabledButtonCommon,
                  ]}
                  onPress={skipExercise}
                  disabled={autoFinish}
                >
                  <SkipForward
                    color={isDark ? "#C4B5FD" : "white"}
                    width={20}
                    height={20}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    isPaused ? styles.playButton : styles.pauseButton,
                    autoFinish && styles.disabledButtonCommon,
                  ]}
                  onPress={togglePause}
                  disabled={autoFinish}
                >
                  {isPaused ? (
                    <Play
                      color={isDark ? "#86EFAC" : "white"}
                      width={20}
                      height={20}
                    />
                  ) : (
                    <Square
                      color={isDark ? "#FCD34D" : "white"}
                      width={20}
                      height={20}
                    />
                  )}
                </TouchableOpacity>
              </View>

              <Text style={styles.exerciseName}>{activeExercise.name}</Text>
            </View>

            <Modal
              visible={showFinishConfirmation}
              transparent={true}
              animationType="fade"
              onRequestClose={() => setShowFinishConfirmation(false)}
            >
              <TouchableOpacity
                style={styles.modalOverlay}
                activeOpacity={1}
                onPress={() => setShowFinishConfirmation(false)}
              >
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={() => {}} // Prevents modal from closing when tapping content
                  style={styles.finishConfirmationBox}
                >
                  <Text style={styles.finishConfirmationTitle}>
                    {t("workoutLogger.finishConfirmation")}
                  </Text>
                  <Text style={styles.finishConfirmationText}>
                    {t("workoutLogger.finishWarning")}
                  </Text>
                  <View style={styles.finishConfirmationButtons}>
                    <TouchableOpacity
                      style={styles.finishConfirmationCancelButton}
                      onPress={() => setShowFinishConfirmation(false)}
                    >
                      <Text style={styles.finishConfirmationCancelText}>
                        {t("workoutLogger.cancel")}
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.finishConfirmationFinishButton}
                      onPress={confirmFinishWorkout}
                    >
                      <Text style={styles.finishConfirmationFinishText}>
                        {t("workoutLogger.confirm")}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              </TouchableOpacity>
            </Modal>

            {autoFinish && (
              <View style={styles.workoutCompleteMessageContainer}>
                <Text style={styles.workoutCompleteMessage}>
                  <Text style={styles.workoutCompleteMessageNote}>
                    {t("workoutLogger.note")}:{" "}
                  </Text>{" "}
                  {t("workoutLogger.workoutComplete")}
                </Text>
              </View>
            )}

            {activeExercise.currentSet === 0 && activeExercise.instructions && (
              <View style={styles.instructionsContainer}>
                <Text style={styles.instructionsText}>
                  {activeExercise.instructions}
                </Text>
              </View>
            )}

            {restTimer === 0 && (
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                  {t("workoutLogger.weight")}
                </Text>
                <TextInput
                  style={styles.input}
                  keyboardType="number-pad"
                  value={weight}
                  onChangeText={setWeight}
                  placeholder={t("workoutLogger.weightPlaceholder")}
                  placeholderTextColor={isDark ? "#9CA3AF" : undefined}
                  editable={!autoFinish}
                />
                <Text style={styles.inputLabel}>
                  {t("workoutLogger.reps", {
                    target: activeExercise.targetReps,
                  })}
                </Text>
                <TextInput
                  style={styles.input}
                  keyboardType="number-pad"
                  value={reps}
                  onChangeText={setReps}
                  placeholder={t("workoutLogger.repsPlaceholder")}
                  placeholderTextColor={isDark ? "#9CA3AF" : undefined}
                  editable={!autoFinish}
                />
                <TouchableOpacity
                  style={styles.completeSetButton}
                  onPress={completeSet}
                  disabled={!weight || !reps || autoFinish}
                >
                  <Text style={styles.completeSetButtonText}>
                    {t("workoutLogger.completeSet")}
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            <View style={styles.statsContainer}>
              <View style={styles.remainingContainer}>
                <View style={[styles.remainingItem, styles.setsRemainingItem]}>
                  <Text
                    style={[styles.remainingLabel, styles.setsRemainingLabel]}
                  >
                    {t("workoutLogger.setsRemaining")}
                  </Text>
                  <Text
                    style={[styles.remainingValue, styles.setsRemainingValue]}
                  >
                    {autoFinish ? 0 : remainingSets}
                  </Text>
                </View>
                <View
                  style={[styles.remainingItem, styles.exercisesRemainingItem]}
                >
                  <Text
                    style={[
                      styles.remainingLabel,
                      styles.exercisesRemainingLabel,
                    ]}
                  >
                    {t("workoutLogger.exercisesRemaining")}
                  </Text>
                  <Text
                    style={[
                      styles.remainingValue,
                      styles.exercisesRemainingValue,
                    ]}
                  >
                    {remainingExercises}
                  </Text>
                </View>
                <View style={[styles.remainingItem, styles.durationItem]}>
                  <Text style={[styles.remainingLabel, styles.durationLabel]}>
                    {t("workoutLogger.workoutDuration")}
                  </Text>
                  <Text style={[styles.remainingValue, styles.durationValue]}>
                    {calculateWorkoutDuration()}
                  </Text>
                </View>
              </View>

              {remainingExercises > 0 && workout && (
                <View style={styles.nextExerciseContainer}>
                  <Text style={styles.nextExerciseLabel}>
                    {t("workoutLogger.nextExercise")}
                  </Text>
                  <Text style={styles.nextExerciseName}>
                    {workout.exercises[currentExerciseIndex + 1].name}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </ScrollView>
    </GestureHandlerRootView>
  );
};

export const WorkoutInfoContainer = () => {
  const { t } = useTranslation();
  const { isDark } = useTheme();
  const styles = createStyles(isDark ? "dark" : "light");

  return (
    <View style={styles.workoutInfoContainer}>
      <View style={styles.workoutInfoHeaderContainer}>
        <Text style={styles.workoutInfoTitle}>{t("workoutInfo.title")} </Text>
        <CircleHelp color={isDark ? "#E9D5FF" : "gray"} size={18} />
      </View>

      <View style={styles.workoutInfoSettingsContainer}>
        <TouchableOpacity style={styles.workoutInfoSettingItem}>
          <View style={styles.workoutInfoSettingContent}>
            <Text style={styles.workoutInfoSettingText}>
              {t("workoutInfo.pauseTimer")}
            </Text>
            <Square color="#9199a5" size={18} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.workoutInfoSettingItem}>
          <View style={styles.workoutInfoSettingContent}>
            <Text style={styles.workoutInfoSettingText}>
              {t("workoutInfo.continueWorkout")}
            </Text>
            <Play color="#56a368" size={18} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.workoutInfoSettingItem}>
          <View style={styles.workoutInfoSettingContent}>
            <Text style={styles.workoutInfoSettingText}>
              {t("workoutInfo.skipExercise")}
            </Text>
            <SkipForward color="#8a64bb" size={18} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.workoutInfoSettingItem}>
          <View style={styles.workoutInfoSettingContent}>
            <Text style={styles.workoutInfoSettingText}>
              {t("workoutInfo.skipRest")}
            </Text>
            <FastForward color="#c59b26" size={18} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.workoutInfoSettingItem}>
          <View style={styles.workoutInfoSettingContent}>
            <Text style={styles.workoutInfoSettingText}>
              {t("workoutInfo.endWorkout")}
            </Text>
            <CircleCheckBig color="#c55f63" size={18} />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};
