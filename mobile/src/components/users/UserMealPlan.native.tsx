import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
} from "react-native";
import {
  Apple,
  Beef,
  Cookie,
  Utensils,
  ChevronDown,
  ChevronUp,
  Info,
  Coffee,
  Wheat,
} from "lucide-react-native";
import { MacroDisplay } from "../meal/MacroDisplay.native";
import {
  MacroRange,
  MealWithFood,
  UserMealPlanProps,
  FoodItem,
  MealCategory,
} from "../../types/meal";
import { mealsApi } from "../../api/meals.api";
import { useTheme } from "../../contexts/ThemeContext";
import { createStyles } from "../../style/UserMeal.style";
import { UserApi } from "../../api/user";
import { useTranslation } from "react-i18next";

interface User {
  isMacrosHidden?: boolean;
}

export const UserMealPlan: React.FC<UserMealPlanProps> = ({ userId }) => {
  const { t } = useTranslation();

  const [meals, setMeals] = useState<MealWithFood[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [foodItems, setFoodItems] = useState<Record<string, FoodItem>>({});
  const [totalMacroRange, setTotalMacroRange] = useState<MacroRange>({
    min: { protein: 0, carbs: 0, fats: 0, calories: 0 },
    max: { protein: 0, carbs: 0, fats: 0, calories: 0 },
  });
  const [user, setUser] = useState<User | null>(null);
  const [expandedMealId, setExpandedMealId] = useState<string | null>(null);
  const { isDark } = useTheme();
  const styles = createStyles(isDark ? "dark" : "light");

  const toggleMealExpand = (mealId: string) => {
    if (expandedMealId === mealId) {
      setExpandedMealId(null);
    } else {
      setExpandedMealId(mealId);
    }
  };

  // Transform fetched meals to match the expected structure
  const transformFetchedMeals = (fetchedMeals: any) => {
    return fetchedMeals.map((meal: any) => {
      const transformedCategories = [];

      // Check if meal has categories
      if (meal.categories && meal.categories.length > 0) {
        // Group food items by category
        const proteinItems = meal.categories.filter(
          (item: any) => item.name === "Protein"
        );
        const carbItems = meal.categories.filter(
          (item: any) => item.name === "Carbs"
        );
        const otherItems = meal.categories.filter(
          (item: any) => item.name === "Other"
        );

        // Create Protein category if it has items
        if (proteinItems.length > 0) {
          transformedCategories.push({
            name: "Protein",
            options: proteinItems[0].options.map((item: any) => ({
              foodId: item.foodId,
              amount: item.amount || 0,
            })),
          });
        }

        // Create Carb category if it has items
        if (carbItems.length > 0) {
          transformedCategories.push({
            name: "Carbs",
            options: carbItems[0].options.map((item: any) => ({
              foodId: item.foodId,
              amount: item.amount || 0,
            })),
          });
        }

        // Create Other category if it has items
        if (otherItems.length > 0) {
          transformedCategories.push({
            name: "Other",
            options: otherItems[0].options.map((item: any) => ({
              foodId: item.foodId,
              amount: item.amount || 0,
            })),
          });
        }
      }

      return {
        ...meal,
        id: meal.id,
        name: meal.name,
        categories: transformedCategories,
        macroRange: meal.macroRange || {
          min: { protein: 0, carbs: 0, fats: 0, calories: 0 },
          max: { protein: 0, carbs: 0, fats: 0, calories: 0 },
        },
        foodItems:
          meal.categories.flatMap((cat: MealCategory) =>
            cat.options.map((opt: any) => opt.food)
          ) || [],
      };
    });
  };

  // Initialize food items from meals' foodItems
  const initializeFoodItems = (mealsList: MealWithFood[]) => {
    const newFoodItems: Record<string, FoodItem> = {};

    mealsList.forEach((meal) => {
      if (meal.foodItems && meal.foodItems.length) {
        meal.foodItems.forEach((item) => {
          // Store the most up-to-date version of the item
          newFoodItems[item.id] = {
            ...item,
            // Make sure we capture any custom serving size defined by the trainer
            defaultServing: item.defaultServing || 100,
          };
        });
      }
    });
    setFoodItems(newFoodItems);
    return newFoodItems;
  };

  // Calculate total macros directly from meal macroRanges
  const calculateTotalMacros = (mealsList: MealWithFood[]) => {
    const total: MacroRange = {
      min: { protein: 0, carbs: 0, fats: 0, calories: 0 },
      max: { protein: 0, carbs: 0, fats: 0, calories: 0 },
    };

    mealsList.forEach((meal) => {
      if (meal.macroRange) {
        // Add min values
        if (meal.macroRange.min) {
          total.min.protein += meal.macroRange.min.protein || 0;
          total.min.carbs += meal.macroRange.min.carbs || 0;
          total.min.fats += meal.macroRange.min.fats || 0;
          total.min.calories += meal.macroRange.min.calories || 0;
        }

        // Add max values
        if (meal.macroRange.max) {
          total.max.protein += meal.macroRange.max.protein || 0;
          total.max.carbs += meal.macroRange.max.carbs || 0;
          total.max.fats += meal.macroRange.max.fats || 0;
          total.max.calories += meal.macroRange.max.calories || 0;
        }
      }
    });

    return total;
  };

  // Function to fetch meals - extracted to be reusable for initial load and refresh
  const fetchMeals = useCallback(async () => {
    try {
      const response = await mealsApi.findByTrainee(userId);

      const transformedMeals = transformFetchedMeals(response);

      // Initialize foodItems from the transformed meals and get the result directly
      const updatedFoodItems = initializeFoodItems(transformedMeals);

      const totalMacros = calculateTotalMacros(transformedMeals);
      setTotalMacroRange(totalMacros);

      setMeals(transformedMeals);
      return updatedFoodItems;
    } catch (err) {
      console.error("Error fetching meals:", err);
      setError(t("errors.failedToLoadUserMealPlan"));
      return null;
    }
  }, [userId]);

  // Function to fetch user
  const fetchUser = useCallback(async () => {
    try {
      const response = await UserApi.getUserDetails(userId);
      setUser(response[0].trainee);
    } catch (err) {
      console.error("Error fetching user:", err);
      setError(t("errors.failedToLoadUserMealPlan"));
    }
  }, [userId]);

  // Initial load of meals and user
  useEffect(() => {
    setLoading(true);
    fetchUser();

    fetchMeals()
      .then(() => setLoading(false))
      .catch(() => setLoading(false));
  }, [fetchMeals, fetchUser]);

  // Handle pull-to-refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    setError(null);
    await fetchMeals();
    setRefreshing(false);
  }, [fetchMeals]);

  // Helper function to extract Hebrew name from combined English-Hebrew name
  const extractHebrewName = (fullName: string): string => {
    if (!fullName) return "Loading...";

    // Check if the name contains a dash (indicating it might have both English and Hebrew)
    if (fullName.includes(" - ")) {
      // Return the part after the dash (Hebrew name)
      return fullName.split(" - ")[1];
    }

    return fullName;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={isDark ? "#E9D5FF" : "#3B82F6"} />
        <Text style={styles.loadingText}>{t("mealPlan.loading")}.</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Text style={styles.retryText} onPress={onRefresh}>
          {t("common.tapToRetry")}
        </Text>
      </View>
    );
  }

  if (meals.length === 0) {
    return (
      <ScrollView
        style={styles.container}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[isDark ? "#E9D5FF" : "#3B82F6"]} // Android
            tintColor={isDark ? "#E9D5FF" : "#3B82F6"} // iOS
          />
        }
      >
        <View style={styles.emptyContainer}>
          <View style={styles.emptyCard}>
            <Text style={styles.emptyText}>{t("mealPlan.noMoreMealPlan")}</Text>
          </View>
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[isDark ? "#E9D5FF" : "#3B82F6"]} // Android
          tintColor={isDark ? "#E9D5FF" : "#3B82F6"} // iOS
        />
      }
    >
      <View style={styles.mealPlanCard}>
        <View style={styles.headerContainer}>
          <View style={styles.iconContainer}>
            <Utensils
              size={24}
              color={isDark ? "rgba(216, 180, 254, 1)" : "#4A90E2"}
            />
          </View>
          <Text style={styles.headerTitle}>{t("mealPlan.dailyMealPlan")}</Text>
        </View>

        <View style={styles.totalMacrosContainer}>
          {!user?.isMacrosHidden && (
            <MacroDisplay
              macroRange={totalMacroRange}
              title={t("mealPlan.totalDailyMacros")}
            />
          )}
        </View>

        <View style={styles.mealsContainer}>
          {meals.map((meal) => {
            const isMealExpanded = expandedMealId === meal.id;
            const totalFoodCount = meal.categories.reduce(
              (count, cat) => count + (cat.options?.length || 0),
              0
            );

            return (
              <View key={meal.id} style={styles.mealCard}>
                {/* Meal Header with Dropdown */}
                <TouchableOpacity
                  activeOpacity={0.8}
                  style={styles.mealHeader}
                  onPress={() => toggleMealExpand(meal.id)}
                >
                  <View style={styles.mealHeaderContent}>
                    <View style={styles.mealIconContainer}>
                      <Utensils
                        size={20}
                        color={isDark ? "rgba(216, 180, 254, 1)" : "#4A90E2"}
                      />
                    </View>
                    <View>
                      <Text style={styles.mealTitle}>{meal.name}</Text>
                      <View style={styles.mealInfoContainer}>
                        <Info
                          size={12}
                          color={isDark ? "#9CA3AF" : "#6B7280"}
                        />
                        <Text style={styles.mealInfoText}>
                          {totalFoodCount} {t("mealPlan.foodItems")}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View style={styles.expandButtonContainer}>
                    {isMealExpanded ? (
                      <ChevronUp
                        size={16}
                        color={isDark ? "rgba(216, 180, 254, 1)" : "#4A90E2"}
                      />
                    ) : (
                      <ChevronDown
                        size={16}
                        color={isDark ? "rgba(216, 180, 254, 1)" : "#4A90E2"}
                      />
                    )}
                  </View>
                </TouchableOpacity>

                {/* Expanded Content */}
                {isMealExpanded && (
                  <View style={styles.expandedContent}>
                    {/* Protein Options */}
                    {meal.categories.find((cat) => cat.name === "Protein") && (
                      <View style={styles.categoryContainer}>
                        <View style={styles.foodCategoryHeader}>
                          <Text style={styles.categoryTitle}>
                            {t("mealPlan.proteinLabel")}
                          </Text>
                        </View>

                        <View style={styles.optionsContainer}>
                          {meal.categories
                            .find((cat) => cat.name === "Protein")
                            ?.options.map((option, idx) => {
                              const food = foodItems[option.foodId];

                              return (
                                <View
                                  key={idx}
                                  style={styles.proteinOptionItem}
                                >
                                  <View style={styles.foodItemContent}>
                                    <View style={styles.foodIconContainer}>
                                      <Beef size={16} color="#FFFFFF" />
                                    </View>

                                    <Text
                                      style={styles.foodItemName}
                                      numberOfLines={1}
                                      ellipsizeMode="tail"
                                    >
                                      {food
                                        ? extractHebrewName(food.name)
                                        : "Loading..."}
                                    </Text>
                                  </View>

                                  <Text style={styles.servingText}>
                                    {t("mealPlan.serving").replace(
                                      "{{amount}}",
                                      option.amount.toString()
                                    )}
                                  </Text>
                                </View>
                              );
                            })}
                        </View>
                      </View>
                    )}

                    {meal.categories.find((cat) => cat.name === "Carbs") && (
                      <View style={styles.categoryContainer}>
                        <View style={styles.carbsCategoryHeader}>
                          <Text style={styles.categoryTitle}>
                            {t("mealPlan.carbLabel")}
                          </Text>
                        </View>

                        <View style={styles.optionsContainer}>
                          {meal.categories
                            .find((cat) => cat.name === "Carbs")
                            ?.options.map((option, idx) => {
                              const food = foodItems[option.foodId];

                              return (
                                <View key={idx} style={styles.carbOptionItem}>
                                  <View style={styles.foodItemContent}>
                                    <View style={styles.carbsIconContainer}>
                                      <Wheat size={16} color="#FFFFFF" />
                                    </View>
                                    <Text
                                      style={styles.foodItemName}
                                      numberOfLines={1}
                                      ellipsizeMode="tail"
                                    >
                                      {food
                                        ? extractHebrewName(food.name)
                                        : "Loading..."}
                                    </Text>
                                  </View>
                                  <Text style={styles.servingText}>
                                    {t("mealPlan.serving").replace(
                                      "{{amount}}",
                                      option.amount.toString()
                                    )}{" "}
                                  </Text>
                                </View>
                              );
                            })}
                        </View>
                      </View>
                    )}

                    {/* Other Options */}
                    {meal.categories.find((cat) => cat.name === "Other") && (
                      <View style={styles.categoryContainer}>
                        <View style={styles.otherCategoryHeader}>
                          <Text style={styles.categoryTitle}>
                            {t("mealPlan.otherLabel")}
                          </Text>
                        </View>

                        <View style={styles.optionsContainer}>
                          {meal.categories
                            .find((cat) => cat.name === "Other")
                            ?.options.map((option, idx) => {
                              const food = foodItems[option.foodId];

                              return (
                                <View key={idx} style={styles.carbOptionItem}>
                                  <View style={styles.foodItemContent}>
                                    <View style={styles.othersIconContainer}>
                                      <Coffee size={16} color="#FFFFFF" />
                                    </View>
                                    <Text
                                      style={styles.foodItemName}
                                      numberOfLines={1}
                                      ellipsizeMode="tail"
                                    >
                                      {food
                                        ? extractHebrewName(food.name)
                                        : "Loading..."}
                                    </Text>
                                  </View>
                                  <Text style={styles.servingText}>
                                    {t("mealPlan.serving").replace(
                                      "{{amount}}",
                                      option.amount.toString()
                                    )}{" "}
                                  </Text>
                                </View>
                              );
                            })}
                        </View>
                      </View>
                    )}

                    {/* Meal Macros */}
                    {!user?.isMacrosHidden && (
                      <MacroDisplay
                        macroRange={meal.macroRange}
                        title={t("mealPlan.mealMacroRange")}
                      />
                    )}
                  </View>
                )}
              </View>
            );
          })}
        </View>
      </View>
    </ScrollView>
  );
};
