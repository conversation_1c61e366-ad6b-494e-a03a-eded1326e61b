import {
        StyleSheet,
        Platform, 
      } from 'react-native';
import { colors } from '../theme/colors';

export const createStyles = (theme: 'light' | 'dark') =>
  StyleSheet.create({
        loadingContainer: {
            justifyContent: 'center',
            alignItems: 'center',
        },
        loadingText: {
            marginTop: 10,
            fontSize: 16,
            color: '#3B82F6',
        },
        container: {
            flex: 1,
            padding: 16,
        },
        workoutLogContainer: {
            backgroundColor: colors[theme].cardBackground,
            borderRadius: 12,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
            padding: 16,
        },
        header: {
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
            justifyContent: 'space-between',
        },
        headerIcon: {
            backgroundColor: '#E0F2FE',
            padding: 8,
            borderRadius: 8,
            marginRight: 8,
        },
        headerText: {
            fontSize: 18,
            fontWeight: '600',
            color: colors[theme].MeterTitle,
            flex: 1,
        },
        selectDayContainer: {
            marginBottom: 24,
        },
        selectDayLabel: {
            fontSize: 14,
            fontWeight: '500',
            color: colors[theme].MeterTitle,
            marginBottom: 8,
        },
        dayPicker: {
            flexDirection: 'row',
            marginBottom: 16,
        },
        dayButton: {
            backgroundColor: '#F3F4F6',
            paddingVertical: 8,
            paddingHorizontal: 16,
            borderRadius: 8,
            marginRight: 8,
        },
        selectedDayButton: {
            backgroundColor: '#BFDBFE',
        },
        dayButtonText: {
            fontSize: 14,
            color: '#4B5563',
        },
        selectedDayButtonText: {
            color: '#1E40AF',
            fontWeight: '500',
        },
        selectDayButton: {
            backgroundColor: colors[theme].Buttonprimary,
            paddingVertical: 12,
            borderRadius: 8,
            alignItems: 'center',
        },
        selectDayButtonText: {
            fontSize: 16,
            fontWeight: '600',
            color: colors[theme].ButtonText,
        },
        restTimerContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#FEF3C7',
            paddingVertical: 4,
            paddingHorizontal: 8,
            borderRadius: 8,
        },
        restTimerText: {
            fontSize: 14,
            fontWeight: '500',
            color: '#B45309',
            marginLeft: 4,
            marginRight: 8,
        },
        skipRestButton: {
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#F59E0B',
            paddingVertical: 4,
            paddingHorizontal: 8,
            borderRadius: 6,
        },
        skipRestText: {
            fontSize: 12,
            fontWeight: '500',
            color: '#FFFFFF',
            marginLeft: 4,
        },
        controlsHelpContainer: {
            marginBottom: 16,
        },
        controlsHelpButton: {
            position: 'absolute',
            top: 0,
            right: 0,
            padding: 8,
        },
        controlsHelpTextContainer: {
            backgroundColor: '#E0F2FE',
            padding: 12,
            borderRadius: 8,
        },
        controlsHelpText: {
            fontSize: 12,
            color: '#3B82F6',
        },
        exerciseContainer: {
            backgroundColor: colors[theme].cardBackground,
            borderWidth:1,
            borderColor: colors[theme].MeterTitle,
            borderRadius: 12,
            padding: 16,
        },
        exerciseHeader: {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 16,
        },
        exerciseName: {
            fontSize: 16,
            fontWeight: '500',
            color: colors[theme].MeterTitle,
            flex: 1,
        },
        exerciseButtons: {
            flexDirection: 'row',
        },
        exerciseButton: {
            backgroundColor: colors[theme].Buttonprimary,
            padding: 8,
            borderRadius: 8,
            marginLeft: 8,
        },
        instructionsContainer: {
            backgroundColor: colors[theme].ServingItem,
            padding: 12,
            borderRadius: 8,
            marginBottom: 16,
        },
        instructionsText: {
            fontSize: 14,
            color: colors[theme].MeterTitle,
        },
        inputContainer: {
            marginBottom: 16,
        },
        inputLabel: {
            fontSize: 14,
            fontWeight: '500',
            color: colors[theme].MeterTitle,
            marginBottom: 4,
        },
        input: {
            backgroundColor: '#FFFFFF',
            paddingVertical: 8,
            paddingHorizontal: 12,
            borderRadius: 8,
            borderWidth: 1,
            borderColor: '#E5E7EB',
            fontSize: 14,
            color: '#374151',
            marginBottom: 12,
        },
        completeSetButton: {
            backgroundColor: colors[theme].Buttonprimary,
            paddingVertical: 12,
            borderRadius: 8,
            alignItems: 'center',
        },
        completeSetButtonText: {
            fontSize: 16,
            fontWeight: '600',
            color: colors[theme].ButtonText,
        },
        statsContainer: {
            marginTop: 20,
            borderRadius: 12,
            padding: 0,
        },
        remainingContainer: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            paddingBottom: 12,
            gap: 8,
        },
        remainingItem: {
            flex: 1,
            alignItems: 'center',
            padding: 12,
            borderRadius: 10,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
            elevation: 2,
        },
        remainingLabel: {
            fontSize: 11,
            fontWeight: '600',
            marginBottom: 6,
            textAlign: 'center',
            textAlignVertical: 'center', // optional, helps center inside parent vertically
          },
          
        setsRemainingItem: {
            backgroundColor: 'rgba(250, 245, 255, 1)',
        },
        exercisesRemainingItem: {
            backgroundColor: 'rgba(240, 253, 244, 1)',
        },
        durationItem: {
            backgroundColor: 'rgba(239, 246, 255, 1)',
        },
    
        setsRemainingLabel: {
            color: 'rgba(107, 33, 168, 1)',
            fontWeight: 400
        },
        exercisesRemainingLabel: {
            color: 'rgba(22, 101, 52, 1)',
            fontWeight: 400
        },
        durationLabel: {
            color: 'rgba(30, 64, 175, 1)',
            fontWeight: 400
        },
    
        remainingValue: {
            fontSize: 22,
            fontWeight: '700',
            textAlign: 'center',
        },
        setsRemainingValue: {
            color: 'rgba(88, 28, 135, 1)',
            fontWeight: 600
        },
        exercisesRemainingValue: {
            color: 'rgba(20, 83, 45, 1)',
            fontWeight: 600
        },
        durationValue: {
            color: 'rgba(30, 58, 138, 1)',
            fontWeight: 600
        },
        finishConfirmationContainer: {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0,0,0,0.4)',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 99,
            elevation: 10
        },
        finishConfirmationBox: {
            backgroundColor: '#FFFFFF',
            borderRadius: 12,
            padding: 24,
            width: '80%',
            maxWidth: 320,
        },
        finishConfirmationTitle: {
            fontSize: 18,
            fontWeight: '600',
            color: colors[theme].MeterTitle,
            marginBottom: 8,
        },
        finishConfirmationText: {
            fontSize: 14,
            color: '#4B5563',
            marginBottom: 24,
        },
        finishConfirmationButtons: {
            flexDirection: 'column',
            width: '100%',
            gap: 12,
        },
        finishConfirmationCancelButton: {
            backgroundColor:colors[theme].Buttonsecondary,
            paddingVertical: 12,
            paddingHorizontal: 24,
            borderRadius: 8,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.2,
            shadowRadius: 2,
            elevation: 2,
            alignItems: 'center',
        },
        finishConfirmationCancelText: {
            fontSize: 16,
            fontWeight: '500',
            color: colors[theme].MeterTitle,
        },
        finishConfirmationFinishButton: {
            backgroundColor: colors[theme].FinsihButton,
            paddingVertical: 12,
            paddingHorizontal: 24,
            borderRadius: 8,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.2,
            shadowRadius: 2,
            elevation: 2,
            alignItems: 'center',
        },
        finishConfirmationFinishText: {
            fontSize: 16,
            fontWeight: '600',
            color: '#FFFFFF',
        },
        nextExerciseContainer: {
            marginTop: 16,
            backgroundColor: 'rgba(255, 259, 262, 1)',
            borderRadius: 8,
            padding: 12,
        },
        nextExerciseLabel: {
            fontSize: 12,
            color: '#6B7280',
            fontWeight: '500',
            marginBottom: 4,
        },
        nextExerciseName: {
            fontSize: 14,
            fontWeight: '600',
            color: 'rgba(31, 41, 55, 1)',
        },
        // at bottom of StyleSheet.create({...})
        errorBanner: {
            backgroundColor: '#FEE2E2',
            borderWidth: 1,
            borderColor: '#F87171',
            borderStyle:'solid',
            padding: 8,
            borderRadius: 8,
            marginBottom: 16,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
        },
        errorText: {
            color: '#B91C1C',
            flex: 1,
            fontSize: 14,
        },
        errorDismiss: {
            color: '#B91C1C',
            fontWeight: '600',
            marginLeft: 12,
        },
    })