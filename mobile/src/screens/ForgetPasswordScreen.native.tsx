import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Mail, Send } from "lucide-react-native";
import { otpApi } from "../api/forget-password.api";
import axios from "axios";
import { useTheme } from "../contexts/ThemeContext";
import { useTranslation } from "react-i18next";

import { createStyles } from "../style/ForgetPassword.style";

type RootStackParamList = {
  Login: undefined;
  ResetPassword: { email: string };
  ForgetPassword: undefined;
};

type ForgetPasswordScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  "ForgetPassword"
>;
interface ForgetPasswordProps {}

export const ForgetPasswordScreen: React.FC<ForgetPasswordProps> = () => {
  const { t } = useTranslation();
  const [email, setEmail] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const navigation = useNavigation<ForgetPasswordScreenNavigationProp>();
  const { isDark } = useTheme();
  const styles = createStyles(isDark ? "dark" : "light");

  // Effect to clear messages after 3 seconds
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (error || success) {
      timeoutId = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 3000);
    }

    // Cleanup function to clear timeout
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [error, success]);

  // Email validation function
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const backendToFrontendErrorMap: Record<string, string> = {
    "email should not be empty": t("forgotPassword.errors.emailRequired"),
    "email must be an email": t("forgotPassword.errors.invalidEmail"),
    "No account found with this email address": t(
      "forgotPassword.errors.accountNotFound"
    ),
    "If an account with this email exists, a password reset OTP has been sent":
      t("forgotPassword.errors.otpSentIfExists"),
    "Email configuration error": t("forgotPassword.errors.emailConfiguration"),
    "Failed to process password reset request": t(
      "forgotPassword.errors.failedRequest"
    ),
  };

  const getForgotPasswordError = (backendMessage: string) =>
    backendToFrontendErrorMap[backendMessage] ||
    t("forgotPassword.errors.unexpectedError");

  const handleSubmit = async () => {
    // Clear previous messages
    setError(null);
    setSuccess(null);

    // Validate email before making API call
    if (!email.trim()) {
      setError(t("errors.emailEmpty"));
      return;
    }

    if (!isValidEmail(email)) {
      setError(t("errors.invalidEmail"));
      return;
    }

    setIsSubmitting(true);

    try {
      // Replace with your actual API call
      await otpApi.forgotPassword({ email });

      setSuccess(t("success.resetInstructions"));

      // Navigate to reset password page after short delay
      setTimeout(() => {
        navigation.navigate("ResetPassword", { email });
      }, 2000);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        // Handle specific error responses
        if (error.response) {
          const backendMessage =
            error.response.data?.message || error.response.data?.error || "";
          setError(getForgotPasswordError(backendMessage));
        } else if (error.request) {
          setError(t("forgotPassword.errors.unexpectedError"));
        } else {
          setError(t("forgotPassword.errors.unexpectedError"));
        }
      } else {
        setError(t("forgotPassword.errors.unexpectedError"));
      }
      console.error("Error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ScrollView
      contentContainerStyle={styles.container}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.header}>
        <Text style={styles.title}>{t("App.name")}</Text>
        <Text style={styles.subtitle}>{t("forgotPassword.subtitle")}</Text>
      </View>

      {/* Error Message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      {/* Success Message */}
      {success && (
        <View style={styles.successContainer}>
          <Text style={styles.successText}>{success}</Text>
        </View>
      )}
      <View style={styles.form}>
        <View style={styles.inputContainer}>
          <View style={styles.iconContainer}>
            <Mail size={20} color="#9CA3AF" />
          </View>

          <TextInput
            style={styles.input}
            placeholder={t("forgotPassword.emailPlaceholder")}
            keyboardType="email-address"
            autoCapitalize="none"
            value={email}
            onChangeText={setEmail}
            placeholderTextColor="#9CA3AF"
          />
        </View>

        <TouchableOpacity
          style={[styles.button, isSubmitting && styles.buttonDisabled]}
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          <View style={styles.buttonContent}>
            <Send size={20} color="#FFFFFF" />
            <Text style={styles.buttonText}>
              {isSubmitting
                ? t("forgotPassword.sending")
                : t("forgotPassword.sendOTP")}
            </Text>
            {isSubmitting && (
              <ActivityIndicator
                size="small"
                color={isDark ? "#E9D5FF" : "#3B82F6"}
                style={styles.loader}
              />
            )}
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.navigate("Login")}
        >
          <Text style={styles.backButtonText}>
            {t("forgotPassword.backToLogin")}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default ForgetPasswordScreen;
