// WorkoutContextTypes.ts
import React, { createContext, useContext, useEffect, useState } from "react";
import { WorkoutSet } from "../types/workout";
import { TrainingDay } from "../types/training";

export interface ActiveExercise {
  workoutExerciseId: string;
  name: string;
  sets: WorkoutSet[];
  currentSet: number;
  rest: string;
  instructions?: string;
  targetReps: string;
  targetSets: number;
}

export interface WorkoutContextType {
  restTimer: number;
  setRestTimer: React.Dispatch<React.SetStateAction<number>>;
  isPaused: boolean;
  setIsPaused: React.Dispatch<React.SetStateAction<boolean>>;
  showActiveWorkoutSessionsModal: boolean;
  setShowActiveWorkoutSessionsModal: React.Dispatch<
    React.SetStateAction<boolean>
  >;
  setActiveTab: React.Dispatch<React.SetStateAction<string>>;
  isWorkoutStarted: boolean;
  setIsWorkoutStarted: React.Dispatch<React.SetStateAction<boolean>>;
  activeExercise: ActiveExercise | null;
  setActiveExercise: React.Dispatch<
    React.SetStateAction<ActiveExercise | null>
  >;
  currentExerciseIndex: number;
  setCurrentExerciseIndex: React.Dispatch<React.SetStateAction<number>>;
  weight: string;
  setWeight: React.Dispatch<React.SetStateAction<string>>;
  reps: string;
  setReps: React.Dispatch<React.SetStateAction<string>>;
  autoFinish: boolean;
  setAutoFinish: React.Dispatch<React.SetStateAction<boolean>>;
  workout: any;
  setWorkout: React.Dispatch<React.SetStateAction<any>>;
  selectedTrainingPlanId: string;
  setSelectedTrainingPlanId: React.Dispatch<React.SetStateAction<string>>;
  selectedTrainingPlan: TrainingDay | null;
  setSelectedTrainingPlan: React.Dispatch<
    React.SetStateAction<TrainingDay | null>
  >;
  trainingPlan: TrainingDay[];
  setTrainingPlan: React.Dispatch<React.SetStateAction<TrainingDay[]>>;
  workoutExerciseIds: string[];
  setWorkoutExerciseIds: React.Dispatch<React.SetStateAction<string[]>>;
  totalWorkoutDuration: number;
  setTotalWorkoutDuration: React.Dispatch<React.SetStateAction<number>>;
  activeTab: string;
  isWorkoutInProgress: boolean;
  setIsWorkoutInProgress: React.Dispatch<React.SetStateAction<boolean>>;
}

const WorkoutContext = createContext<WorkoutContextType | undefined>(undefined);

export const WorkoutProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // All your state here
  const [activeTab, setActiveTab] = useState("overview");
  const [isWorkoutInProgress, setIsWorkoutInProgress] = useState(false);
  const [restTimer, setRestTimer] = useState(0);
  const [totalWorkoutDuration, setTotalWorkoutDuration] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [workout, setWorkout] = useState<any>(null);
  const [selectedTrainingPlanId, setSelectedTrainingPlanId] =
    useState<string>("");
  const [selectedTrainingPlan, setSelectedTrainingPlan] =
    useState<TrainingDay | null>(null);
  const [trainingPlan, setTrainingPlan] = useState<TrainingDay[]>([]);
  const [showActiveWorkoutSessionsModal, setShowActiveWorkoutSessionsModal] =
    useState<boolean>(false);
  const [isWorkoutStarted, setIsWorkoutStarted] = useState(false);
  const [activeExercise, setActiveExercise] = useState<ActiveExercise | null>(
    null
  );
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [weight, setWeight] = useState("");
  const [reps, setReps] = useState("");
  const [autoFinish, setAutoFinish] = useState(false);
  const [workoutExerciseIds, setWorkoutExerciseIds] = useState<string[]>([]);

  // Timers and effects
  useEffect(() => {
    let interval: number;
    let workoutInterval: number;

    if (restTimer > 0 && !isPaused) {
      interval = window.setInterval(() => {
        setRestTimer((prev) => prev - 1);
      }, 1000);
    }

    if (isWorkoutInProgress && !isPaused) {
      workoutInterval = window.setInterval(() => {
        setTotalWorkoutDuration((prev) => prev + 1);
      }, 1000);
    }

    return () => {
      clearInterval(interval);
      clearInterval(workoutInterval);
    };
  }, [restTimer, isPaused, isWorkoutInProgress]);

  return (
    <WorkoutContext.Provider
      value={{
        activeTab,
        setActiveTab,
        isWorkoutInProgress,
        setIsWorkoutInProgress,
        restTimer,
        setRestTimer,
        totalWorkoutDuration,
        setTotalWorkoutDuration,
        isPaused,
        setIsPaused,
        workout,
        setWorkout,
        selectedTrainingPlanId,
        setSelectedTrainingPlanId,
        selectedTrainingPlan,
        setSelectedTrainingPlan,
        trainingPlan,
        setTrainingPlan,
        showActiveWorkoutSessionsModal,
        setShowActiveWorkoutSessionsModal,
        isWorkoutStarted,
        setIsWorkoutStarted,
        activeExercise,
        setActiveExercise,
        currentExerciseIndex,
        setCurrentExerciseIndex,
        weight,
        setWeight,
        reps,
        setReps,
        autoFinish,
        setAutoFinish,
        workoutExerciseIds,
        setWorkoutExerciseIds,
      }}
    >
      {children}
    </WorkoutContext.Provider>
  );
};

export const useWorkoutContext = () => {
  const context = useContext(WorkoutContext);
  if (context === undefined) {
    throw new Error("useWorkoutContext must be used within a WorkoutProvider");
  }
  return context;
};
