export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  status: "active" | "inactive" | "attention";
  performance: number;
  team: "beginner" | "intermediate" | "advanced";
  lastActive: string;
  overdueTasks: number;
  attentionNote: string | null;
  trainerId: string; // Reference to trainer ID
}

export interface Trainer {
  id: string;
  name: string;
  email: string;
  avatar: string;
  mobileNumber: string;
  trainerR<PERSON>: "senior_trainer" | "junior_trainer" | "nutrition_specialist";
  status: "active" | "inactive" | "training";
  specialties: string[];
  joinDate: string;
  clients: string[];
}
