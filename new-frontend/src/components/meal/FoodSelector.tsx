import React from "react";
import { FoodItem } from "../../types/food";
import { Trash2, Plus, Minus, Utensils, Wheat, Apple } from "lucide-react";

interface FoodSelectorProps {
  food: FoodItem;
  amount: number;
  onAmountChange: (amount: number) => void;
  onRemove: () => void;
}

export const FoodSelector: React.FC<FoodSelectorProps> = ({
  food,
  amount,
  onAmountChange,
  onRemove,
}) => {
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);

    if (!isNaN(value) && value > 0) {
      if (food.minServing && value < food.minServing) return;
      if (food.maxServing && value > food.maxServing) return;
      onAmountChange(value);
    }
  };

  // Calculate macros for current amount
  const macros = {
    protein: Math.round((food.macrosPer100g.protein * amount) / 100),
    carbs: Math.round((food.macrosPer100g.carbs * amount) / 100),
    fats: Math.round((food.macrosPer100g.fats * amount) / 100),
    calories: Math.round((food.macrosPer100g.calories * amount) / 100),
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "protein":
        return <Utensils className="h-4 w-4 text-white" />;
      case "carbs":
        return <Wheat className="h-4 w-4 text-white" />;
      case "other":
        return <Apple className="h-4 w-4 text-white" />;
      default:
        return null;
    }
  };

  const increaseAmount = () => {
    if (!food.maxServing || amount < food.maxServing) {
      onAmountChange(amount + 10);
    }
  };

  const decreaseAmount = () => {
    if (!food.minServing || amount > food.minServing) {
      onAmountChange(amount - 10);
    }
  };

  return (
    <div className="grid grid-cols-12 gap-2 items-center bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors duration-200 group">
      {/* Food Name */}
      <div className="col-span-4 font-medium text-gray-200 truncate flex items-center gap-1">
        <div
          className={`p-1 rounded-full ${
            food.category === "protein"
              ? "bg-purple-900"
              : food.category === "carbs"
              ? "bg-amber-800"
              : "bg-green-900"
          }`}
        >
          {getCategoryIcon(food.category)}
        </div>
        <span className="ml-1 truncate">{food.name}</span>
      </div>

      {/* Amount Controls */}
      <div className="col-span-2 flex items-center justify-between bg-gray-900 rounded p-1 border border-gray-700">
        <button
          onClick={decreaseAmount}
          disabled={!!food.minServing && amount <= food.minServing}
          className="p-0.5 text-gray-400 hover:text-purple-400 disabled:opacity-50"
        >
          <Minus className="h-3 w-3" />
        </button>

        <input
          type="number"
          value={amount}
          onChange={handleAmountChange}
          className="w-10 text-center py-0 border-0 bg-transparent text-white text-xs focus:ring-0"
          min={food.minServing}
          max={food.maxServing}
        />

        <button
          onClick={increaseAmount}
          disabled={!!food && !!food.maxServing && amount >= food.maxServing}
          className="p-0.5 text-gray-400 hover:text-purple-400 disabled:opacity-50"
        >
          <Plus className="h-3 w-3" />
        </button>
      </div>

      {/* Macros - styled like exercise metrics */}
      <div className="col-span-1 text-center bg-purple-900 rounded p-0.5 text-purple-200 text-xs">
        {macros.protein}גרם
      </div>

      <div className="col-span-1 text-center bg-amber-800 rounded p-0.5 text-amber-200 text-xs">
        {macros.carbs}גרם
      </div>

      <div className="col-span-1 text-center bg-green-900 rounded p-0.5 text-green-200 text-xs">
        {macros.fats}גרם
      </div>

      <div className="col-span-2 text-center bg-red-900 rounded p-0.5 text-red-200 text-xs">
        {macros.calories} קל'
      </div>

      {/* Delete Button */}
      <div className="col-span-1 flex justify-center">
        <button
          onClick={onRemove}
          className="p-1 hover:bg-red-900 rounded-full transition-colors opacity-70 group-hover:opacity-100"
          title="הסר פריט"
        >
          <Trash2 className="h-3 w-3 text-red-400" />
        </button>
      </div>
    </div>
  );
};
