# Stage 1: Build
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy only necessary files for install
COPY new-frontend/package.json new-frontend/package-lock.json* ./ 

# Install dependencies
RUN npm install

# Copy the rest of the app
COPY new-frontend/ .

# Build the frontend
RUN npm run build

# Stage 2: Serve with NGINX
FROM nginx:stable-alpine

# Copy built files to NGINX public directory
COPY --from=builder /app/dist /usr/share/nginx/html

# Optional: Replace default NGINX config
# COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Start NGINX
CMD ["nginx", "-g", "daemon off;"]